import pypandoc
import os
import re

# Specify your directories 
source_directory = os.getenv('SOURCE_DIR')
output_directory = os.getenv('OUTPUT_DIR')

def convert_md_to_html(source_directory, output_directory):
    os.makedirs(output_directory, exist_ok=True)
    # Regex to find links that end with .md
    md_link_pattern = re.compile(r'href="([^"]+\.md)"')

    for filename in os.listdir(source_directory):
        if filename.endswith('.md'):
            markdown_file = os.path.join(source_directory, filename)
            html_file = os.path.join(output_directory, filename.replace('.md', '.html'))

            # Convert Markdown file to HTML
            html_content = pypandoc.convert_file(markdown_file, 'html')

            # Replace .md links with .html in the HTML content
            modified_html_content = md_link_pattern.sub(lambda m: 'href="' + m.group(1).replace('.md', '.html') + '"', html_content)

            # Write the modified HTML content to file
            with open(html_file, 'w') as f:
                f.write(modified_html_content)
            
            print(f'Converted {markdown_file} to {html_file}')

if __name__ == "__main__":
    convert_md_to_html(source_directory, output_directory)
