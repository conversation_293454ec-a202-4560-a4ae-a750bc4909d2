﻿using System.Data;
using DBUtils;
using StandardUtils;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using CSG.Common.ExtensionMethods;

namespace GenesysAdapter;

#nullable enable
class SurveyData
{
    private readonly ILogger? _logger;
    private TimeZoneInfo _timeZone;
    private TimeSpan _maxSyncSpan;

    public SurveyData(ILogger? logger, TimeZoneInfo timeZone, TimeSpan? syncSpan)
    {
        _logger = logger;
        _timeZone = timeZone;
        _maxSyncSpan = syncSpan ?? TimeSpan.FromDays(1);
    }

    private class SurveyDataRow
    {
        private DataRow _row;
        TimeZoneInfo _customerTimeZone;

        public SurveyDataRow(DataRow row, TimeZoneInfo timeZone)
        {
            _row = row;
            _customerTimeZone = timeZone;
        }

        public SurveyDataRow(DataRow row, TimeZoneInfo timeZone, DataRow cloneFrom) : this(row, timeZone)
        {
            foreach (DataColumn col in cloneFrom.Table.Columns)
            {
                _row[col.ColumnName] = cloneFrom[col.ColumnName];
            }
        }

        public DataRow DataRow { get => _row; }

        public object? this[string key]
        {
            get => _row[key];
            set
            {
                if (value != _row[key])
                {
                    _row[key] = value ?? DBNull.Value;
                    FieldChanged(key);
                }
            }
        }

        private void FieldChanged(string key)
        {
            // Check if there is a local time field and if so, keep it in sync.
            if (key.EndsWith("ltc", StringComparison.OrdinalIgnoreCase))
                throw new ReadOnlyException("Unexpected attempt to set local time field " + key + "Ltc");
            if (_row.Table.Columns.Contains(key + "Ltc"))
            {
                var ltcColumnName = _row.Table.Columns[key + "Ltc"]?.ColumnName;
                if (this[key] == null
                    || this[key] == DBNull.Value
                    || (DateTime)this[key]! == DateTime.MinValue)
                {
                    _row[key] = DBNull.Value;
                    _row[key + "Ltc"] = DBNull.Value;
                }
                else
                    _row[key + "Ltc"] = TimeZoneInfo.ConvertTimeFromUtc((DateTime)_row[key], _customerTimeZone);
            }

            // Don't modify the updated time for polling.
            if (key.Equals("lastPoll", StringComparison.OrdinalIgnoreCase))
                return;

            if (!key.Equals("updated", StringComparison.OrdinalIgnoreCase))
                _row["updated"] = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Synchronise survey data from Genesys Cloud.
    /// </summary>
    /// <remarks>
    /// <para>
    /// Calls Genesys Cloud APIs:
    /// <list>
    /// <item>
    /// POST /api/v2/analytics/surveys/aggregates/query
    /// </item>
    /// <item>
    /// GET /api/v2/quality/surveys/{surveyId}
    /// </item>
    /// </list>
    /// </para>
    /// <para>
    /// Process flow:
    /// <list>
    /// <item>
    /// Query database for incomplete surveys up to incompleteSurveysExpire days
    /// old
    /// </item>
    /// <item>
    /// Query Genesys for survey aggregates from the previous successful sync to
    /// current time or max sync span.
    /// </item>
    /// <item>
    /// Query Genesys for the survey details for each conversation from the two
    /// previous steps.
    /// </item>
    /// <item>
    /// Update the surveyData, surveyQuestionAnswers and
    /// surveyQuestionGroupScores tables with any updated records
    /// </item>
    /// </para>
    /// <para>
    /// </remarks>
    public void UpdateGCSurveyData()
    {
        // When to stop polling incomplete surveys in the database to see if they have been completed.
        var incompleteSurveysExpire = DateTime.UtcNow.Subtract(TimeSpan.FromDays(90));
        // When to stop polling incomplete surveys frequently.
        var pollSlowlyAfter  = DateTime.UtcNow.Subtract(TimeSpan.FromDays(2));
        // How often to poll recently sent incomplete surveys.
        var pollFrequencyFast = DateTime.UtcNow.Subtract(TimeSpan.FromHours(4));
        // How often to poll older incomplete surveys.
        var pollFrequencySlow = DateTime.UtcNow.Subtract(TimeSpan.FromDays(1));

        System.Collections.Generic.Dictionary<string, DataRow?> surveyIdsToQuery = new();
        System.Diagnostics.Stopwatch timer = System.Diagnostics.Stopwatch.StartNew();
        DBUtils.DBUtils db = new();
        db.Initialize();
        GenesysCloudUtils.SurveyData GCSurveyData = new(_logger);

        // STEP 1: Get all surveys to poll from the database
        //
        // The Genesys aggregate API only returns survey data in the time span
        // the survey was created in, and not again when the survey is
        // complete, so incomplete surveys need to be kept in the database
        // for quick reference, and to allow efficient polling to monitor for
        // survey changes until the survey is complete. This helps minimise the
        // number of API calls.
        //
        // Query database for incomplete surveys up to incompleteSurveysExpire
        // Poll recent incomplete surveys more frequently and older incomplete
        // surveys less frequently.
        // TODO: Switch to SQL bind parameters when the database code is uplifted to support it.
        string sql = string.Format(
            "SELECT * FROM surveyData " +
            "WHERE completedDate IS NULL " +
            "AND updated >= '{0}' " +
            "AND (" +
            "  lastPoll <= '{3}' " +
            "  OR (lastPoll <= '{2}' AND updated >= '{1}')" +
            ")",
            incompleteSurveysExpire.ToString("s"),  // 0 - when to stop polling incomplete surveys.
            pollSlowlyAfter.ToString("s"),          // 1 - when to switch to polling slower.
            pollFrequencyFast.ToString("s"),        // 2 - how often to poll recently sent incomplete surveys.
            pollFrequencySlow.ToString("s")         // 3 - how often to poll older incomplete surveys.
        );
        using DataTable? surveyData = db.GetSQLTableData(sql, nameof(surveyData));
        if (surveyData == null)
            throw new DataException($"Failed to retrieve {nameof(surveyData)} table");
        Console.WriteLine("{0}: {1} surveys with incomplete surveys to process from the database",
            nameof(surveyData),
            surveyData.Rows.Count);

        foreach (DataRow row in surveyData.Rows)
        {
            if (row["surveyId"] == null)
                throw new NullReferenceException(string.Format("Null survey ID from {0} table", nameof(surveyData)));
            surveyIdsToQuery.Add(row["surveyId"].ToString()!, row);
        }

        DateTime lastSync = db.GetSyncLastUpdate(nameof(surveyData));
        DateTime syncTo = DateTime.UtcNow.Subtract(TimeSpan.FromMinutes(1));
        if (syncTo > lastSync.Add(_maxSyncSpan))
            syncTo = lastSync.Add(_maxSyncSpan);
        DateTime syncFrom = lastSync.Subtract(TimeSpan.FromHours(6)); // TODO: Reduce when Genesys libraray is more reliable. 1 minute should be fine.

        Console.WriteLine(
            "{0}: sync {1}Z to {2}Z",
            nameof(surveyData),
            syncFrom.ToString("s"),
            syncTo.ToString("s"));

        if (syncFrom >= syncTo)
            return;

        // POST /api/v2/analytics/surveys/aggregates/query
        // Grouped by surveyId
        // Just used to get a list of surveys that were sent in the period.
        // NOTE: Surveys only appear in aggregates at the time they are sent.
        //       The sent time will be shortly after the conversation ends.
        GenesysCloudDefSurveys.SurveyAggregations gcSurveyAggregations = GCSurveyData
            .GetSurveyAggregationDataFromGC(syncFrom, syncTo);

        _logger?.LogInformation("{JobName}: {SurveyCount} surveys in period from Genesys",
            nameof(surveyData),
            gcSurveyAggregations.results?.Length ?? 0);
        if (gcSurveyAggregations.results != null)
        {
            foreach (GenesysCloudDefSurveys.Result result in gcSurveyAggregations.results)
            {
                if (result.group?.surveyId is null)
                    throw new NullReferenceException("Null survey ID from Genesys when querying aggregates");
                // Ensure that duplicate keys are not added.
                if (!surveyIdsToQuery.ContainsKey(result.group.surveyId))
                    surveyIdsToQuery.Add(result.group.surveyId, null);
            }
        }

        Console.WriteLine("{0}: {1} total surveys to poll for updates",
            nameof(surveyData),
            surveyIdsToQuery.Count);

        if (surveyIdsToQuery.Count == 0)
        {
            db.SetSyncLastUpdate(syncTo, nameof(surveyData));
            Console.WriteLine("{0}: took {1:N3} secs",
                nameof(surveyData),
                timer.Elapsed.TotalSeconds);
            return;
        }

        using DataTable surveyQuestionGroupScores = db.GetSQLTableSchema(nameof(surveyQuestionGroupScores));
        using DataTable? surveyQuestionAnswers = db.GetSQLTableSchema(nameof(surveyQuestionAnswers));

        foreach (string surveyId in surveyIdsToQuery.Keys)
        {
            if (surveyId == null)
                throw new NoNullAllowedException("Survey ID is null");

            // GET /api/v2/quality/surveys/{surveyId}
            GenesysCloudDefSurveys.Survey? gcSurvey = null;
            try
            {
                gcSurvey = GCSurveyData.GetSurveyFromGC(surveyId);

                // Handle null response (survey not found/deleted)
                if (gcSurvey == null)
                {
                    _logger?.LogDebug("Survey {SurveyId} not found or deleted. Skipping this survey.", surveyId);
                    continue; // Skip this survey and continue with the next one
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger?.LogWarning(ex, "Access forbidden for survey {SurveyId}. Skipping this survey.", surveyId);
                continue; // Skip this survey and continue with the next one
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving survey {SurveyId} from Genesys Cloud", surveyId);

                // If this is a survey from the database (not a new one), we can continue
                // Otherwise, we should fail to prevent missing surveys
                if (surveyIdsToQuery[surveyId] == null)
                {
                    _logger?.LogError("Failed to retrieve new survey {SurveyId}, terminating to prevent data loss", surveyId);
                    throw;
                }

                _logger?.LogWarning("Failed to retrieve existing survey {SurveyId}, will retry later", surveyId);
                continue;
            }

            DateTime updatedTime = DateTime.UtcNow;

            // Validate survey ID matches (gcSurvey should not be null at this point due to earlier check)
            if (surveyId != gcSurvey.id)
            {
                var msg = string.Format("Survey ID mismatch: requested {0} but received '{1}'", surveyId, gcSurvey?.id);
                _logger?.LogError("Survey ID mismatch for {SurveyId}: {Message}", surveyId, msg);

                if (surveyIdsToQuery[surveyId] == null)
                {
                    _logger?.LogError("Failed to retrieve new survey {SurveyId} due to ID mismatch, terminating to prevent data loss", surveyId);
                    throw new DataException(msg);
                }

                _logger?.LogWarning("Failed to retrieve existing survey {SurveyId} due to ID mismatch, will retry later", surveyId);
                continue;
            }
            SurveyDataRow surveyRow = new(surveyIdsToQuery[surveyId] ?? surveyData.NewRow(), _timeZone);
            surveyRow["surveyId"] = surveyId;
            surveyRow["conversationId"] = gcSurvey.conversation?.id;
            surveyRow["surveyFormId"] = gcSurvey.surveyForm?.id;
            surveyRow["surveyName"] = gcSurvey.surveyForm?.name;
            surveyRow["agentId"] = gcSurvey.agent?.id;
            surveyRow["agentTeamId"] = gcSurvey.agentTeam?.id;
            surveyRow["queueId"] = gcSurvey.queue?.id;
            surveyRow["status"] = gcSurvey.status;
            surveyRow["totalScore"] = gcSurvey.answers?.totalScore;
            surveyRow["completedDate"] = gcSurvey.completedDate;
            surveyRow["lastPoll"] = updatedTime;
            if (surveyIdsToQuery[surveyId] is null)
                surveyData.Rows.Add(surveyRow.DataRow);

            if (gcSurvey.answers?.questionGroupScores is null)
                continue;

            // surveyQuestionGroupScores table
            foreach (GenesysCloudDefSurveys.QuestionGroupScore groupScore in gcSurvey.answers.questionGroupScores)
            {
                if (groupScore is null)
                    continue;

                GenesysCloudDefSurveys.QuestionGroup? questionGroup = gcSurvey
                    .surveyForm?
                    .GetQuestionGroup(groupScore.questionGroupId);

                DataRow row = surveyQuestionGroupScores.NewRow();
                row["surveyId"] = surveyId;
                row["conversationId"] = gcSurvey.conversation?.id;
                row["surveyFormId"] = gcSurvey.surveyForm?.id;
                row["surveyName"] = gcSurvey.surveyForm?.name;
                row["agentId"] = gcSurvey.agent?.id;
                row["agentTeamId"] = gcSurvey.agentTeam?.id;
                row["queueId"] = gcSurvey.queue?.id;
                row["questionGroupId"] = groupScore.questionGroupId;
                row["questionGroupName"] = questionGroup?.name;
                row.SetFieldValue(surveyId, "questionGroupTotalScore", groupScore.totalScore);
                row.SetFieldValue(surveyId, "questionGroupMaxTotalScore", groupScore.maxTotalScore);
                row["questionGroupMarkedNa"] = groupScore.markedNA;
                row["updated"] = updatedTime;
                surveyQuestionGroupScores.Rows.Add(row);

                // surveyQuestionAnswers table
                if (groupScore.questionScores is null)
                    continue;
                foreach (GenesysCloudDefSurveys.QuestionScore questionScore in groupScore.questionScores)
                {
                    GenesysCloudDefSurveys.Question? question = questionGroup?.GetQuestion(questionScore.questionId);
                    string? answerText = null;
                    decimal? answerValue = null;
                    switch (question?.type)
                    {
                        case "freeTextQuestion":
                            answerText = questionScore.freeTextAnswer;
                            break;
                        default:
                            GenesysCloudDefSurveys.AnswerOption? answer = question?
                                .GetAnswerOption(questionScore.answerId);
                            answerText = answer?.text;
                            answerValue = answer?.value;
                            break;
                    }

                    row = surveyQuestionAnswers.NewRow();
                    row["surveyId"] = surveyId;
                    row["conversationId"] = gcSurvey.conversation?.id;
                    row["surveyFormId"] = gcSurvey.surveyForm?.id;
                    row.SetFieldValue(surveyId, "surveyName", gcSurvey.surveyForm?.name);
                    row["agentId"] = gcSurvey.agent?.id;
                    row["agentTeamId"] = gcSurvey.agentTeam?.id;
                    row["queueId"] = gcSurvey.queue?.id;
                    row["questionGroupId"] = groupScore.questionGroupId;
                    row.SetFieldValue(surveyId, "questionGroupName", questionGroup?.name);
                    row["questionId"] = questionScore.questionId;
                    row.SetFieldValue(surveyId, "questionText", question?.text);
                    row["questionType"] = question?.type;
                    row["questionAnswerId"] = questionScore.answerId;
                    row.SetFieldValue(surveyId, "questionAnswerText", answerText);
                    row.SetFieldValue(surveyId, "questionAnswerValue", answerValue);
                    row.SetFieldValue(surveyId, "questionScore", questionScore.score);
                    row.SetFieldValue(surveyId, "questionMarkedNa", questionScore.markedNA);
                    row["updated"] = updatedTime;
                    surveyQuestionAnswers.Rows.Add(row);
                }
            }
        }

        Console.WriteLine("{0}: total {1} rows to merge",
            nameof(surveyData),
            surveyData.Rows.Count);

        if (surveyData.Rows.Count > 0)
            db.WriteSQLDataBulk(surveyData);

        if (surveyQuestionGroupScores.Rows.Count > 0)
        {
            Console.WriteLine("{0}: total {1} rows to merge",
                nameof(surveyQuestionGroupScores),
                surveyQuestionGroupScores.Rows.Count);

            db.WriteSQLDataBulk(surveyQuestionGroupScores);
        }

        if (surveyQuestionAnswers.Rows.Count > 0)
        {
            Console.WriteLine("{0}: total {1} rows to merge",
                nameof(surveyQuestionAnswers),
                surveyQuestionAnswers.Rows.Count);

            db.WriteSQLDataBulk(surveyQuestionAnswers);
        }

        db.SetSyncLastUpdate(syncTo, nameof(surveyData));
        Console.WriteLine("{0}: took {1:N3} secs",
            nameof(surveyData),
            timer.Elapsed.TotalSeconds);
    }
}
#nullable restore
