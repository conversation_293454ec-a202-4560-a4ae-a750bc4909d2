﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using Chilkat;
using Newtonsoft.Json;
using RealCN = RealUserPushConversations;
using RealUA = RealUserPushActivityDef;
using RealUC = RealUserPushCallStatsDef;
using StandardUtils;
using StringBuilder = System.Text.StringBuilder;
using UserReal = GenesysCloudDefUserRealtime;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace GCRealTime
{
    public class UserRealTime
    {
        const string APISockURL = "streaming.mypurecloud.com.au";

        // Constants for Genesys Cloud limits
        private const int MAX_TOPICS_PER_CHANNEL = 999; // Genesys Cloud limit is 1000 topics per channel
        private const int MAX_CHANNELS_PER_OAUTH_CLIENT = 20; // Genesys Cloud limit

        // Static counter to track the total number of channels created across all instances
        private static int _totalChannelsCreated = 0;
        private static readonly object _channelCountLock = new object();
        public String SyncType { get; set; }
        private DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
        private ChilkatJson ChilKatJsonObj = new ChilkatJson();
        private String APIKey = String.Empty;
        private String CustomerKeyID = String.Empty;
        private DataTable DTUserDetails = new DataTable();
        private DataTable DTQueueDetails = new DataTable();
        private DataTable DTQueueObservations = new DataTable();
        private DataTable DTUserData = new DataTable();
        private DataTable DTUserCallsDets = new DataTable();
        private DataTable DTQueueCallsDets = new DataTable();
        private WebSocketDetail GCWebSocketAct = new WebSocketDetail();
        private WebSocketDetail GCWebSocketAdh = new WebSocketDetail();
        private WebSocketDetail GCWebSocketCalls = new WebSocketDetail();
        private WebSocketDetail GCWebSocketCallDets = new WebSocketDetail();
        private WebSocketDetail GCWebSocketQueueCallDets = new WebSocketDetail();
        private WebSocketDetail GCWebSocketQueueObs = new WebSocketDetail();
        private System.Timers.Timer Process;
        private DateTime LastChannelUpd;
        private DateTime LastTermUpd;
        private Boolean WriteUserDataAct = false;
        private Boolean WriteUserDataAdh = false;
        private Boolean WriteUserDataCalls = false;
        private Boolean WriteUserDataCallsDets = false;
        private Boolean WriteQueueDataCallsDets = false;
        public int TotalErrors;
        public bool ShouldExit = false;


        private DataTable? ClientFeatures { get; set; }
        public string? TimeZoneConfig { get; set; }
        private readonly ILogger? _logger;
        private string JsonSearchString = string.Empty;


        public UserRealTime(ILogger logger)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            Utils Utils = new Utils();

            Console.WriteLine("Starting MultiThread");

            DBAdapter.Initialize();
            ChilKatJsonObj.Initialize();

            CustomerKeyID = Utils.ReadSetting("CSG_CUSTOMERKEYID");

            ClientFeatures = Utils.GetGCCustomerConfig();

            APIKey = ChilKatJsonObj.APIKey;

            TimeZoneConfig = Convert.ToString(ClientFeatures.Rows[0]["datetimezone"]);

            //Boolean Successful = DBAdapter.ExecuteSQLQuery("Delete from userRealTimeData");

            LastChannelUpd = DateTime.Now;
            LastTermUpd = DateTime.Now;
            TotalErrors = 0;


            switch (SyncType)
            {
                case "userActivity":
                    ClearTable("userRealTimeData");
                    DTUserDetails = GetUsers();
                    DTUserData = GetUserStatus();
                    if (DTUserData.Rows.Count > 0)
                    {
                        DBAdapter.WriteSQLDataBulk(DTUserData);
                    }
                    break;
                case "userCalls":
                    DTUserDetails = GetUsers();
                    DTUserCallsDets = DBAdapter.CreateInMemTable("userRealTimeConvData");
                    break;
                case "queueCalls":
                    DTQueueDetails = GetQueues();
                    DTQueueCallsDets = DBAdapter.CreateInMemTable("queueRealTimeConvData");
                    break;

            }

        }
        public void GetConversationStatus()
        {
            List<string> ConversationIds = new List<string>();
            List<int> rowsToUpdateIndexes = new List<int>();
            List<int> DeleteConversationRows = new List<int>();

            try
            {
                foreach (DataRow row in DTQueueCallsDets.Rows.Cast<DataRow>().ToList())
                {
                    // Null handling for startDate and updatedDate
                    if (row["startdate"] == DBNull.Value || row["updated"] == DBNull.Value)
                    {
                        continue;
                    }

                    DateTime startDate;
                    DateTime updatedDate;

                    if (!DateTime.TryParse(row["startdate"].ToString(), System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out startDate) ||
                        !DateTime.TryParse(row["updated"].ToString(), System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out updatedDate))
                    {
                        continue;
                    }

                    TimeSpan difference = updatedDate - startDate;
                    double differenceInMinutes = difference.TotalMinutes;

                    if (row["manuallychecked"] != DBNull.Value && Convert.ToBoolean(row["manuallychecked"]))
                    {
                        if (differenceInMinutes > 60 && row["media"].ToString() != "email")
                        {
                            row["manuallychecked"] = false;
                        }
                        else
                        {
                            continue;
                        }
                    }

                    if (differenceInMinutes > 5)
                    {
                        ConversationIds.Add(row["conversationid"].ToString());
                        rowsToUpdateIndexes.Add(DTQueueCallsDets.Rows.IndexOf(row));
                    }
                }

                if (ConversationIds.Count > 0)
                {
                    int batchSize = 100;
                    int numBatches = (int)Math.Ceiling((double)ConversationIds.Count / batchSize);

                    for (int batchIndex = 0; batchIndex < numBatches; batchIndex++)
                    {
                        List<string> batchConversationIds = ConversationIds.Skip(batchIndex * batchSize).Take(batchSize).ToList();
                        string ConversationIdsString = string.Join(",", batchConversationIds);

                        try
                        {
                            string JsonString = ChilKatJsonObj.ReturnJson("/api/v2/analytics/conversations/details?id=" + ConversationIdsString);

                            if (!string.IsNullOrEmpty(JsonString) && JsonString != "{}")
                            {
                                dynamic jsonObject = JsonConvert.DeserializeObject(JsonString);
                                JArray conversations = jsonObject["conversations"];

                                foreach (JToken conversation in conversations)
                                {
                                    string conversationId = conversation["conversationId"].ToString();

                                    for (int i = 0; i < rowsToUpdateIndexes.Count; i++)
                                    {
                                        int rowIndex = rowsToUpdateIndexes[i];
                                        if (rowIndex >= 0 && rowIndex < DTQueueCallsDets.Rows.Count)
                                        {
                                            DataRow row = DTQueueCallsDets.Rows[rowsToUpdateIndexes[i]];
                                            if (row["conversationid"].ToString() == conversationId)
                                            {
                                                row["manuallychecked"] = true;
                                                Console.WriteLine("{0} Conversation ID: {1} from Conversation Details Api has been manually checked", DateTime.UtcNow, conversationId);

                                                if (conversation["conversationEnd"] != null)
                                                {
                                                    row["conversationstate"] = "disconnected";
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("Error during API call for conversations: {0}\n{1}", ConversationIdsString, ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("An error occurred: {0}", ex.Message);
                throw;
            }
        }
        public void CreateWebSocket(string SocketAddress, string SocketChannel, string ThreadName)
        {
            Chilkat.Rest ChilRest = new Chilkat.Rest();
            Chilkat.WebSocket ChilWebSocket = new Chilkat.WebSocket();

            Process = new System.Timers.Timer(3000);
            Process.Elapsed += new System.Timers.ElapsedEventHandler(this.TimerCallBack);
            Process.AutoReset = true;
            Process.Enabled = true;

            bool success = ChilRest.Connect(SocketAddress, 443, true, false);
            if (success != true)
            {
                _logger.LogError(ChilRest.LastErrorText);
                return;
            }


            success = ChilWebSocket.UseConnection(ChilRest);
            if (success != true)
            {
                _logger.LogError(ChilWebSocket.LastErrorText);
                return;
            }

            ChilWebSocket.AddClientHeaders();

            string ResponseBody = ChilRest.FullRequestNoBody("GET", SocketChannel);
            success = ChilWebSocket.ValidateServerHandshake();
            if (success != true)
            {
                _logger.LogError(ChilWebSocket.LastErrorText);
                Console.WriteLine(ResponseBody);
                Console.WriteLine(ChilRest.ResponseHeader);
                return;
            }

            // Re-trigger the getQueueStatus in a separate thread
            // QueueObsRealTime QueueInit = new QueueObsRealTime();
            // QueueInit.Initialize();

            // QueueInit.DTQueueDetails = DTQueueDetails;

            Console.WriteLine("Starting Receive");
            while (! ShouldExit)
            {
                try{
                Boolean SuccessFul = ChilWebSocket.ReadFrame();
                //Console.WriteLine("Frame Received. Type {0}", ChilWebSocket.FrameOpcodeInt);
                if (SuccessFul == true)
                {
                    if (ChilWebSocket.FrameOpcodeInt == 1)
                    {
                        // Console.WriteLine("*/Start ReadFrame :{0}", SuccessFul);
                        TotalErrors = 0;
                        string ReceivedJson = ChilWebSocket.GetFrameData();
                        // Console.WriteLine("\nReceived:{0}",ReceivedJson);
                        ReceiveData(ReceivedJson, ThreadName);
                        //Console.WriteLine("\nReceived:{0} SyncType {1} Records in Buffer {2}", DateTime.Now,SyncType,DtRealTimeData.Rows.Count);
                    }
                }
                else
                {
                    _logger.LogWarning("No Data");
                    Interlocked.Add(ref TotalErrors, 1);

                    if (TotalErrors > 100)
                    {
                        _logger.LogError(ChilWebSocket.LastErrorText);
                        Console.WriteLine(ResponseBody);
                        Console.WriteLine(ChilRest.ResponseHeader);
                        //NLogger.Fatal(CustomerKeyID + "Ending The Real Time Adapter Thread:  " + ThreadName + " Version:" + Version + " At: " + DateTime.Now + " Error Count Exceeds Allowed: Closing Application Exit Code :30000");
                        //Errors.SendErrorMessage(CustomerKeyID, "ENDREAL", "Ending The Real Time Adapter Thread:  " + ThreadName + " At: " + DateTime.Now + " Error Count Exceeds Allowed: Closing Application Exit Code :30000");

                        // Unsubscribe from channels when exiting
                        string SocketID = string.Empty;
                        try
                        {
                            Uri uri = new Uri(SocketChannel);
                            string[] segments = uri.AbsolutePath.Split('/');
                            int channelIndex = Array.IndexOf(segments, "channels") + 1;
                            SocketID = (channelIndex >= 0 && channelIndex < segments.Length) ? segments[channelIndex] : string.Empty;

                            string Response = ChilRest.FullRequestNoBody("DELETE", "/api/v2/notifications/channels/" + SocketID + "/subscriptions");

                            Console.WriteLine("Deleted subscriptions from {0}", SocketID);
                        }
                        catch
                        {
                            Console.WriteLine("Failed to deleted subscriptions from {0}", SocketID);
                        }

                        throw new ApplicationException(
                            $"Ending The Real Time Adapter Thread '{ThreadName}'. Error Count Exceeds Allowed"
                        );
                    }
                    ReceiveData("{\"topicName\": \"channel.metadata\", \"eventBody\": {\"message\": \"WebSocket Error\"}}", ThreadName);

                    }
                    //return;
                }
                catch (ThreadAbortException ex)
                {
                    _logger.LogError("Thread was aborted: " + ex.Message);
                    break;
                }
                catch (ThreadInterruptedException  ex)
                {
                    _logger.LogError("Thread was aborted: " + ex.Message);
                    break;
                }
            }
            _logger.LogWarning($"Thread '{ThreadName}' exiting because a shutdown was requested.");
        }

        private void ReceiveData(String JsonString, string ThreadName)
        {
            //Console.WriteLine("Json Received:\n{0}", JsonString);

            AlertObject Notification = new AlertObject();

            if (JsonString.IndexOf("WebSocket Heartbeat") > 0)
            {
                Console.Write("==============================================================================================\n{0} Hrt Beat:{1}\n" +
                              "==============================================================================================\n", ThreadName, DateTime.Now);

                if ((DateTime.Now - LastChannelUpd).TotalSeconds > 21600)
                {
                    LastChannelUpd = DateTime.Now;
                    Console.Write("{0} Redo WebSockets:{1}\n", ThreadName, DateTime.Now);

                    if (ChilKatJsonObj.GetAuthAPIKey() != true)
                    {
                        APIKey = ChilKatJsonObj.APIKey;
                        throw new ApplicationException("ChilKat: Cannot Obtain API Key - Do Not Proceed");
                    }
                    else
                    {
                        Console.WriteLine("Regenerated The API For Thread :{0} Now {1}", ThreadName, APIKey);
                        Console.WriteLine("Redoing the Subs");

                        switch (ThreadName)
                        {
                            case "userActivity":
                                Console.WriteLine("Renewing User Activity Job");
                                CreateUserActivitySubs(GCWebSocketAct);
                                CreateUserAdherenceSubs(GCWebSocketAdh);
                                CreateUserCallSubs(GCWebSocketCalls);
                                CreateUserCallDetSubs(GCWebSocketCallDets);
                                break;
                            case "userRealTimeConvData":
                                Console.WriteLine("Renewing User Conversation Job");
                                // CreateUserConvSubs(GCWebSocket);
                                break;
                            case "queuerealtimeconvData":
                                Console.WriteLine("Renewing Queue Conversation Job");
                                // CreateQueueConvSubs(GCWebSocket);
                                break;
                        }

                    }

                }
            }
            else if (JsonString.IndexOf("WebSocket Error") > 0)
            {
                Console.Write("{0} Error Packet:{1}\nError Count :{2}", ThreadName, DateTime.Now, TotalErrors);
            }
            else if(JsonString.IndexOf("Websocket closing soon") > 0)
            {
                Console.WriteLine("Websocket closing soon for : {0}, /n {1}", ThreadName, JsonString);
                switch (ThreadName)
                {
                    case "userActivity":
                        Console.WriteLine("Renewing User Activity Job");
                        StartUserActivity();
                        break;
                    case "userRealTimeConvData":
                        Console.WriteLine("Renewing User Conversation Job");
                        StartUserCallDets();
                        break;
                    case "queuerealtimeconvData":
                        Console.WriteLine("Renewing Queue Conversation Job");
                        StartQueueCallDets();
                        break;
                }
            }
            else
            {
                Notification = JsonConvert.DeserializeObject<AlertObject>(JsonString,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });

                string NotificationClass = Notification.topicName.ToLower().Split('.')[1];
                string NotificationType = string.Empty;
                if (NotificationClass == "users")
                    NotificationType = Notification.topicName.ToLower().Split('.')[3];
                else if(NotificationClass =="analytics")
                    NotificationType = Notification.topicName.ToLower().Split('.')[4];
                else
                    NotificationType = Notification.topicName.ToLower().Split('.')[2];

                //Console.Write("Not:{0}:", NotificationType);
                switch (NotificationType)
                {
                    case "workforcemanagement":
                        TransAdherence(JsonString);
                        break;
                    case "conversationsummary":
                        TransCalls(JsonString);
                        break;
                    case "conversations":
                        TransUserCallDets(JsonString);
                        break;
                    case "activity":
                        TransActivity(JsonString);
                        break;
                    case "queues":
                        TransQConv(JsonString);
                        break;
                    default:
                        Console.Write("Unknown:{0}\nJSON:\n{1}", NotificationType, JsonString);
                        break;
                }
            }
        }

        private void UserUpdate()
        {
            //NLogger.Debug("Changes to Users Calls:{0}", WriteUserDataCallsDets);
            //NLogger.Debug("Changes to Queue Calls:{0}", WriteQueueDataCallsDets);

            if (WriteUserDataAct || WriteUserDataAdh || WriteUserDataCalls)
            {
                lock (DTUserData)
                {
                    if (DTUserData != null)
                    {
                        DataTable xDataTable = DTUserData.GetChanges();
                        DTUserData.AcceptChanges();
                        Console.Write("WrtUS:");

                        int Attempts = 0;
                        while (Attempts < 2)
                        {
                            if (xDataTable != null)
                            {
                                if (xDataTable.TableName != null)
                                {
                                    try
                                    {
                                        DBAdapter.WriteSQLDataBulk(xDataTable);
                                        break;
                                    }
                                    catch
                                    {
                                        Thread.Sleep(500);
                                        Attempts++;
                                    }
                                }
                                else
                                {
                                    Console.WriteLine("User Table Name is null");
                                    break;
                                }
                            }
                            else
                            {
                                Console.WriteLine("User Table is null");
                                break;
                            }
                        }

                        if (Attempts > 1)
                        {
                            _logger.LogError("User Conversation Sync Corrupted - Aborting - {0}", DateTime.Now);
                            System.Environment.Exit(-10056);
                        }

                        if (WriteUserDataAct)
                            WriteUserDataAct = false;
                        if (WriteUserDataAdh)
                            WriteUserDataAdh = false;
                        if (WriteUserDataCalls)
                            WriteUserDataCalls = false;
                    }
                }

            }

            if (WriteUserDataCallsDets)
            {
                lock (DTUserCallsDets)
                {

                    Console.Write("WrtUCD:");
                    int Attempts = 0;

                    //Delete Rows

                    DTUserCallsDets.AcceptChanges();
                    foreach (DataRow DRCheck in DTUserCallsDets.Rows)
                    {
                        switch (DRCheck["media"].ToString())
                        {
                            case "voice":
                                if (((string)DRCheck["conversationstate"] == "terminated" || (string)DRCheck["conversationstate"] == "disconnected")
                                        && ((bool)DRCheck["acwstate"] == false))
                                    DRCheck.Delete();
                                Console.Write("D:");
                                break;

                            case "email":
                            case "callback":
                            case "chat":
                            case "messages":
                                if ((string)DRCheck["conversationstate"] == "disconnected"
                                        && ((bool)DRCheck["acwstate"] == false))
                                    DRCheck.Delete();
                                Console.Write("D:");
                                break;
                        }
                    }
                    DTUserCallsDets.AcceptChanges();

                    while (Attempts < 2)
                    {
                        Boolean Successful = DBAdapter.WriteSQLDataSync(DTUserCallsDets, "userRealTimeConvData");

                        if (Successful == true)
                            break;
                        else
                        {
                            Thread.Sleep(500);
                            Attempts++;

                        }

                    }

                    if (Attempts > 1)
                    {
                        _logger.LogError("User Conversation Sync Corrupted - Aborting - {0}", DateTime.Now);
                        System.Environment.Exit(-10056);
                    }

                    WriteUserDataCallsDets = false;
                }
            }

            if (WriteQueueDataCallsDets)
            {
                lock (DTQueueCallsDets)
                {

                    Console.Write("WrtUCD:");
                    int Attempts = 0;

                    //Delete Rows
                    GetConversationStatus();

                    DTQueueCallsDets.AcceptChanges();
                    foreach (DataRow DRCheck in DTQueueCallsDets.Rows)
                    {
                        switch (DRCheck["media"].ToString())
                        {
                            case "voice":
                                if (((string)DRCheck["conversationstate"] == "terminated" || (string)DRCheck["conversationstate"] == "disconnected"))
                                {
                                    object acwStateValue = DRCheck["acwstate"];
                                    if (acwStateValue == DBNull.Value || (acwStateValue is bool && !(bool)acwStateValue))
                                    {
                                        DRCheck.Delete();
                                        Console.Write("D:");
                                    }
                                }
                                else
                                {
                                    DRCheck["updated"] = DateTime.UtcNow;
                                }
                                break;

                            case "email":
                            case "callback":
                            case "chat":
                            case "messages":
                                if ((string)DRCheck["conversationstate"] == "disconnected")
                                {
                                    object acwStateValue = DRCheck["acwstate"];
                                    if (acwStateValue == DBNull.Value || (acwStateValue is bool && !(bool)acwStateValue))
                                    {
                                        DRCheck.Delete();
                                        Console.Write("D:");
                                    }
                                }
                                else
                                {
                                    DRCheck["updated"] = DateTime.UtcNow;
                                }
                                break;

                        }
                    }
                    DTQueueCallsDets.AcceptChanges();

                    while (Attempts < 2)
                    {
                        Boolean Successful = DBAdapter.WriteSQLDataSync(DTQueueCallsDets, "queueRealTimeConvData");

                        if (Successful == true)
                            break;
                        else
                        {
                            Thread.Sleep(500);
                            Attempts++;

                        }

                    }

                    if (Attempts > 1)
                    {
                        _logger.LogError("User Conversation Sync Corrupted - Aborting - {0}", DateTime.Now);
                        System.Environment.Exit(-10056);
                    }

                    WriteQueueDataCallsDets = false;
                }
            }

        }

        public void StartUserRealTime()
        {
            StartUserActivity();
            StartUserAdherence();

        }

        public void StartUserActivity()
        {
            String SyncName = "userActivity";
            CreateActivityTable();

            // Check if we need multiple channels for user subscriptions
            if (DTUserDetails.Rows.Count > MAX_TOPICS_PER_CHANNEL)
            {
                _logger.LogInformation($"User count ({DTUserDetails.Rows.Count}) exceeds maximum topics per channel ({MAX_TOPICS_PER_CHANNEL}). Creating multiple channels.");
                CreateMultipleUserChannels(SyncName, CreateUserActivitySubs);
            }
            else
            {
                // Use a single channel for all users
                GCWebSocketAct = CreateChannel(SyncName);
                CreateUserActivitySubs(GCWebSocketAct);
                CreateWebSocket(GCWebSocketAct);
            }
        }

        public void StartUserAdherence()
        {
            String SyncName = "userAdherence";
            CreateAdherenceTable();

            // Check if we need multiple channels for user subscriptions
            if (DTUserDetails.Rows.Count > MAX_TOPICS_PER_CHANNEL)
            {
                _logger.LogInformation($"User count ({DTUserDetails.Rows.Count}) exceeds maximum topics per channel ({MAX_TOPICS_PER_CHANNEL}). Creating multiple channels.");
                CreateMultipleUserChannels(SyncName, CreateUserAdherenceSubs);
            }
            else
            {
                // Use a single channel for all users
                GCWebSocketAdh = CreateChannel(SyncName);
                CreateUserAdherenceSubs(GCWebSocketAdh);
                CreateWebSocket(GCWebSocketAdh);
            }
        }

        public void StartUserCalls()
        {
            String SyncName = "userCallStats";
            CreateCallStatsTable();

            // Check if we need multiple channels for user subscriptions
            if (DTUserDetails.Rows.Count > MAX_TOPICS_PER_CHANNEL)
            {
                _logger.LogInformation($"User count ({DTUserDetails.Rows.Count}) exceeds maximum topics per channel ({MAX_TOPICS_PER_CHANNEL}). Creating multiple channels.");
                CreateMultipleUserChannels(SyncName, CreateUserCallSubs);
            }
            else
            {
                // Use a single channel for all users
                GCWebSocketCalls = CreateChannel(SyncName);
                CreateUserCallSubs(GCWebSocketCalls);
                CreateWebSocket(GCWebSocketCalls);
            }
        }

        public void StartUserCallDets()
        {
            string SyncName = "userCallDets";
            DBAdapter.ExecuteSQLQuery("Delete from userRealTimeConvData");
            CreateCallDetsTable();

            // Check if we need multiple channels for user subscriptions
            if (DTUserDetails.Rows.Count > MAX_TOPICS_PER_CHANNEL)
            {
                _logger.LogInformation($"User count ({DTUserDetails.Rows.Count}) exceeds maximum topics per channel ({MAX_TOPICS_PER_CHANNEL}). Creating multiple channels.");
                CreateMultipleUserChannels(SyncName, CreateUserCallDetSubs);
            }
            else
            {
                // Use a single channel for all users
                GCWebSocketCallDets = CreateChannel(SyncName);
                CreateUserCallDetSubs(GCWebSocketCallDets);
                CreateWebSocket(GCWebSocketCallDets);
            }
        }


        public void StartQueueCallDets()
        {
            string SyncName = "queueCallDets";
            DBAdapter.ExecuteSQLQuery("Delete from queueRealTimeConvData");

            CreateQueueCallDetsTable();

            // Process all observations first
            _logger.LogInformation("Processing queue observations data");
            QueueObsRealTime QueueInit = new QueueObsRealTime();
            QueueInit.Initialize();
            QueueInit.DTQueueDetails = DTQueueDetails;
            QueueInit.getQueueStatus();

            DataTable DTTempQueue = DBAdapter.CreateInMemTable("queuerealtimeconvdata");
            DataTable DTTempUser = DBAdapter.CreateInMemTable("userrealtimeconvdata");

            if (QueueInit.DTQueueConvActive != null && QueueInit.DTQueueConvActive.Rows.Count > 0)
            {
                foreach (DataRow DrConv in QueueInit.DTQueueConvActive.Rows)
                {
                    DTTempQueue.ImportRow(DrConv);

                    if (DrConv["actingas"].ToString() == "agent")
                        DTTempUser.ImportRow(DrConv);
                }

                _logger.LogInformation($"Rows From Obs {DTTempQueue.Rows.Count}");

                if (DTTempQueue != null && DTTempQueue.Rows.Count > 0)
                {
                    DBAdapter.WriteSQLDataSync(DTTempQueue, "queueRealTimeConvData");
                    DTQueueCallsDets = DTTempQueue.Copy();
                }

                if (DTTempUser != null && DTTempUser.Rows.Count > 0)
                {
                    DBAdapter.WriteSQLDataBulk(DTTempUser);
                    DTUserCallsDets = DTTempUser.Copy();
                }

                DTTempQueue.Dispose();
                DTTempUser.Dispose();
            }

            // Now create WebSocket subscriptions after all observations are processed
            DateTime subscriptionStartTime = DateTime.UtcNow;
            _logger.LogInformation($"{subscriptionStartTime} QRT:: Creating WebSocket subscriptions for queues");

            // Check if we need multiple channels for queue subscriptions
            if (DTQueueDetails.Rows.Count > MAX_TOPICS_PER_CHANNEL)
            {
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Queue count ({DTQueueDetails.Rows.Count}) exceeds maximum topics per channel ({MAX_TOPICS_PER_CHANNEL}). Creating multiple channels.");
                DateTime multiChannelStartTime = DateTime.UtcNow;
                CreateMultipleQueueChannels(SyncName);
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Multiple channels created in {(DateTime.UtcNow - multiChannelStartTime).TotalMilliseconds} ms");
            }
            else
            {
                // Use a single channel for all queues
                DateTime singleChannelStartTime = DateTime.UtcNow;
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Creating single channel for {DTQueueDetails.Rows.Count} queues");
                GCWebSocketQueueCallDets = CreateChannel(SyncName);
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Channel created in {(DateTime.UtcNow - singleChannelStartTime).TotalMilliseconds} ms");

                DateTime subsStartTime = DateTime.UtcNow;
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Creating queue subscriptions");
                CreateQueueCallDetSubs(GCWebSocketQueueCallDets);
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Subscriptions created in {(DateTime.UtcNow - subsStartTime).TotalMilliseconds} ms");

                DateTime wsStartTime = DateTime.UtcNow;
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: Creating WebSocket");
                CreateWebSocket(GCWebSocketQueueCallDets);
                _logger.LogInformation($"{DateTime.UtcNow} QRT:: WebSocket created in {(DateTime.UtcNow - wsStartTime).TotalMilliseconds} ms");
            }
            _logger.LogInformation($"{DateTime.UtcNow} QRT:: WebSocket subscriptions completed in {(DateTime.UtcNow - subscriptionStartTime).TotalMilliseconds} ms");
        }

        private void CreateWebSocket(WebSocketDetail GCWebSocket)
        {
            _logger.LogInformation("Creating Channel For: {ReportName}", GCWebSocket.ReportName);
            CreateWebSocket(APISockURL, GCWebSocket.connectUri, GCWebSocket.ReportName);

            while (!ShouldExit)
            {
                _logger.LogDebug("Thread Cycling - {Time}", DateTime.Now);
                Thread.Sleep(3000);
            }
        }

        private void CreateMultipleUserChannels(string baseSyncName, Action<WebSocketDetail, int, int> createSubscriptionsMethod)
        {
            int totalUsers = DTUserDetails.Rows.Count;
            int maxUsersPerChannel = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel
            int channelsNeeded = (int)Math.Ceiling((double)totalUsers / maxUsersPerChannel);

            // Check if creating these channels would exceed the 20-channel limit
            lock (_channelCountLock)
            {
                if (_totalChannelsCreated + channelsNeeded > MAX_CHANNELS_PER_OAUTH_CLIENT)
                {
                    _logger.LogWarning("Creating {ChannelsNeeded} more channels would exceed the Genesys Cloud limit of {MaxChannels} channels per OAuth client. Current count: {CurrentCount}",
                        channelsNeeded, MAX_CHANNELS_PER_OAUTH_CLIENT, _totalChannelsCreated);

                    // Adjust the number of channels to stay within limits
                    int availableChannels = MAX_CHANNELS_PER_OAUTH_CLIENT - _totalChannelsCreated;
                    if (availableChannels <= 0)
                    {
                        _logger.LogError("Cannot create any more channels. Already at the maximum of {MaxChannels}. Consider using a different OAuth client or optimizing channel usage.",
                            MAX_CHANNELS_PER_OAUTH_CLIENT);
                        return; // Cannot proceed without channels
                    }
                    else
                    {
                        _logger.LogWarning("Limiting to {AvailableChannels} channels instead of the requested {ChannelsNeeded}",
                            availableChannels, channelsNeeded);

                        // Adjust to use the available channels
                        maxUsersPerChannel = (int)Math.Ceiling((double)totalUsers / availableChannels);
                        channelsNeeded = availableChannels;
                    }
                }
            }

            _logger.LogInformation("Creating {ChannelsNeeded} channels for {TotalUsers} users", channelsNeeded, totalUsers);

            // Create a list to hold all the WebSocket channels
            List<WebSocketDetail> userChannels = new List<WebSocketDetail>();
            List<Thread> channelThreads = new List<Thread>();

            for (int i = 0; i < channelsNeeded; i++)
            {
                string channelName = $"{baseSyncName}_{i+1}";
                int startIndex = i * maxUsersPerChannel;
                int endIndex = Math.Min(startIndex + maxUsersPerChannel - 1, totalUsers - 1);

                _logger.LogInformation("Creating channel {ChannelNumber} of {TotalChannels} for users {StartIndex} to {EndIndex}",
                    i+1, channelsNeeded, startIndex, endIndex);

                // Create a new channel
                WebSocketDetail channel = CreateChannel(channelName);
                userChannels.Add(channel);

                // Create subscriptions for this batch of users
                createSubscriptionsMethod(channel, startIndex, endIndex);

                // Start a new thread for this channel
                Thread channelThread = new Thread(() =>
                {
                    try
                    {
                        CreateWebSocket(channel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in channel thread for {ChannelName}", channelName);
                    }
                });

                channelThread.Name = channelName;
                channelThread.Start();
                channelThreads.Add(channelThread);
            }

            // Wait for the first channel to be established before returning
            // This ensures at least one channel is active
            if (channelThreads.Count > 0)
            {
                channelThreads[0].Join(5000); // Wait up to 5 seconds
            }
        }

        private void CreateMultipleQueueChannels(string baseSyncName)
        {
            int totalQueues = DTQueueDetails.Rows.Count;
            int maxQueuesPerChannel = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel
            int channelsNeeded = (int)Math.Ceiling((double)totalQueues / maxQueuesPerChannel);

            // Check if creating these channels would exceed the 20-channel limit
            lock (_channelCountLock)
            {
                if (_totalChannelsCreated + channelsNeeded > MAX_CHANNELS_PER_OAUTH_CLIENT)
                {
                    _logger.LogWarning("Creating {ChannelsNeeded} more channels would exceed the Genesys Cloud limit of {MaxChannels} channels per OAuth client. Current count: {CurrentCount}",
                        channelsNeeded, MAX_CHANNELS_PER_OAUTH_CLIENT, _totalChannelsCreated);

                    // Adjust the number of channels to stay within limits
                    int availableChannels = MAX_CHANNELS_PER_OAUTH_CLIENT - _totalChannelsCreated;
                    if (availableChannels <= 0)
                    {
                        _logger.LogError("Cannot create any more channels. Already at the maximum of {MaxChannels}. Consider using a different OAuth client or optimizing channel usage.",
                            MAX_CHANNELS_PER_OAUTH_CLIENT);
                        return; // Cannot proceed without channels
                    }
                    else
                    {
                        _logger.LogWarning("Limiting to {AvailableChannels} channels instead of the requested {ChannelsNeeded}",
                            availableChannels, channelsNeeded);

                        // Adjust to use the available channels
                        maxQueuesPerChannel = (int)Math.Ceiling((double)totalQueues / availableChannels);
                        channelsNeeded = availableChannels;
                    }
                }
            }

            _logger.LogInformation("Creating {ChannelsNeeded} channels for {TotalQueues} queues", channelsNeeded, totalQueues);

            // Create a list to hold all the WebSocket channels
            List<WebSocketDetail> queueChannels = new List<WebSocketDetail>();
            List<Thread> channelThreads = new List<Thread>();

            for (int i = 0; i < channelsNeeded; i++)
            {
                string channelName = $"{baseSyncName}_{i+1}";
                int startIndex = i * maxQueuesPerChannel;
                int endIndex = Math.Min(startIndex + maxQueuesPerChannel - 1, totalQueues - 1);

                _logger.LogInformation("Creating channel {ChannelNumber} of {TotalChannels} for queues {StartIndex} to {EndIndex}",
                    i+1, channelsNeeded, startIndex, endIndex);

                // Create a new channel
                WebSocketDetail channel = CreateChannel(channelName);
                queueChannels.Add(channel);

                // Create subscriptions for this batch of queues
                CreateQueueCallDetSubs(channel, startIndex, endIndex);

                // Start a new thread for this channel
                Thread channelThread = new Thread(() =>
                {
                    try
                    {
                        CreateWebSocket(channel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in channel thread for {ChannelName}", channelName);
                    }
                });

                channelThread.Name = channelName;
                channelThread.Start();
                channelThreads.Add(channelThread);
            }

            // Wait for the first channel to be established before returning
            // This ensures at least one channel is active
            if (channelThreads.Count > 0)
            {
                channelThreads[0].Join(5000); // Wait up to 5 seconds
            }
        }

        private DataTable GetUsers()
        {

            DataTable Users = DBAdapter.GetSQLTableData("select id,name from userDetails where state = 'active'", "UserDetails");

            return Users;

        }

        private DataTable GetQueues()
        {
            DataTable Queues = DBAdapter.GetSQLTableData("select id,name from QueueDetails", "QueueDetails");

            return Queues;

        }

        private DataTable CreateAdherenceTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userAdherenceData";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("adherenceState", typeof(String));
            DTTemp.Columns.Add("adherenceChangeTime", typeof(DateTime));
            DTTemp.Columns.Add("impact", typeof(String));
            DTTemp.Columns.Add("scheduledActivityCategory", typeof(String));
            return DTTemp;
        }

        private DataTable CreateCallDetsTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userCallDets";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("conversationid", typeof(String));
            return DTTemp;
        }

        private DataTable CreateQueueCallDetsTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "queueCallDets";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("conversationid", typeof(String));
            return DTTemp;
        }
        private DataTable CreateActivityTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userActivityData";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("routingStatus", typeof(String));
            DTTemp.Columns.Add("routingDate", typeof(DateTime));
            DTTemp.Columns.Add("systemPresence", typeof(String));
            DTTemp.Columns.Add("presenceId", typeof(String));
            DTTemp.Columns.Add("presenceDate", typeof(DateTime));
            return DTTemp;
        }

        private DataTable CreateCallStatsTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userCallStatData";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("cccallactive", typeof(int));
            DTTemp.Columns.Add("cccallacw", typeof(int));
            DTTemp.Columns.Add("othcallactive", typeof(int));
            DTTemp.Columns.Add("othcallacw", typeof(int));
            DTTemp.Columns.Add("cbcallactive", typeof(int));
            DTTemp.Columns.Add("cbcallacw", typeof(int));
            DTTemp.Columns.Add("cbothcallactive", typeof(int));
            DTTemp.Columns.Add("cbothcallacw", typeof(int));
            DTTemp.Columns.Add("ccemailactive", typeof(int));
            DTTemp.Columns.Add("ccemailacw", typeof(int));
            DTTemp.Columns.Add("othemailactive", typeof(int));
            DTTemp.Columns.Add("othemailacw", typeof(int));
            DTTemp.Columns.Add("ccchatactive", typeof(int));
            DTTemp.Columns.Add("ccchatacw", typeof(int));
            DTTemp.Columns.Add("othchatactive", typeof(int));
            DTTemp.Columns.Add("othchatacw", typeof(int));

            return DTTemp;
        }

        private void ClearTable(String RealTimeName)
        {
            int rowsAffected = DBAdapter.ExecuteSqlNonQuery("Delete from " + RealTimeName);

            Console.WriteLine("\nCleared Table - {1} rows affected: {0}", rowsAffected, RealTimeName);
        }

        private WebSocketDetail CreateChannel(string ReportName)
        {
            WebSocketDetail WSSocket = new WebSocketDetail();

            string JsonString = ChilKatJsonObj.ReturnJson("/api/v2/notifications/channels", "");

            WSSocket = JsonConvert.DeserializeObject<WebSocketDetail>(JsonString,
                   new JsonSerializerSettings
                   {
                       NullValueHandling = NullValueHandling.Ignore
                   });

            WSSocket.ReportName = ReportName;

            // Increment the channel count
            lock (_channelCountLock)
            {
                _totalChannelsCreated++;
                _logger.LogInformation($"Created channel {ReportName}. Total channels: {_totalChannelsCreated}/{MAX_CHANNELS_PER_OAUTH_CLIENT}");
            }

            return WSSocket;
        }


        private void CreateQueueCallDetSubs(WebSocketDetail WebSock, int startIndex = 0, int endIndex = -1)
        {
            _logger.LogInformation("Creating Call Summary Channel For Queues");

            // Create Subscriptions for Notification - Calls
            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            int maxSubscriptions = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel

            // If endIndex is not specified, use all remaining rows
            if (endIndex == -1 || endIndex >= DTQueueDetails.Rows.Count)
            {
                endIndex = DTQueueDetails.Rows.Count - 1;
            }

            _logger.LogInformation("Creating subscriptions for queues from index {StartIndex} to {EndIndex}", startIndex, endIndex);

            for (int i = startIndex; i <= endIndex; i++)
            {
                if (i >= DTQueueDetails.Rows.Count) break;

                DataRow DRRow = DTQueueDetails.Rows[i];
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.routing.queues." + DRRow["id"].ToString() + ".conversations\"},");
                ++Counter;

                if (Counter >= maxSubscriptions)
                    break;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ChilKatJsonObj.ReturnJson(URL, JSONBodyString);

                //Console.WriteLine(JSONBodyString);

            }


        }

        private void CreateUserCallDetSubs(WebSocketDetail WebSock, int startIndex = 0, int endIndex = -1)
        {
            _logger.LogInformation("Creating Call Summary Channel For Users");

            // Create Subscriptions for Notification - Calls
            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            int maxSubscriptions = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel

            // If endIndex is not specified, use all remaining rows
            if (endIndex == -1 || endIndex >= DTUserDetails.Rows.Count)
            {
                endIndex = DTUserDetails.Rows.Count - 1;
            }

            _logger.LogInformation("Creating subscriptions for users from index {StartIndex} to {EndIndex}", startIndex, endIndex);

            for (int i = startIndex; i <= endIndex; i++)
            {
                if (i >= DTUserDetails.Rows.Count) break;

                DataRow DRRow = DTUserDetails.Rows[i];
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".conversations\"},");
                ++Counter;

                if (Counter >= maxSubscriptions)
                    break;
            }

            //Console.WriteLine("Call Dets Subscription for the following agents: {0}", SubscriptionJSON.ToString());
            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ChilKatJsonObj.ReturnJson(URL, JSONBodyString);

                //Console.WriteLine(JSONBodyString);

            }


        }

        private void CreateUserCallSubs(WebSocketDetail WebSock, int startIndex = 0, int endIndex = -1)
        {
            _logger.LogInformation("Creating Call Summary Channel For Users");

            // Create Subscriptions for Notification - Calls
            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            int maxSubscriptions = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel

            // If endIndex is not specified, use all remaining rows
            if (endIndex == -1 || endIndex >= DTUserDetails.Rows.Count)
            {
                endIndex = DTUserDetails.Rows.Count - 1;
            }

            _logger.LogInformation("Creating subscriptions for users from index {StartIndex} to {EndIndex}", startIndex, endIndex);

            for (int i = startIndex; i <= endIndex; i++)
            {
                if (i >= DTUserDetails.Rows.Count) break;

                DataRow DRRow = DTUserDetails.Rows[i];
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".conversationsummary\"},");
                ++Counter;

                if (Counter >= maxSubscriptions)
                    break;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ChilKatJsonObj.ReturnJson(URL, JSONBodyString);

                //Console.WriteLine(JSONBodyString);

            }


        }

        private void CreateUserActivitySubs(WebSocketDetail WebSock, int startIndex = 0, int endIndex = -1)
        {
            _logger.LogInformation("Creating Activity Channel For Users");

            // Create Subscriptions for Notification - Activity and WFM
            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            int maxSubscriptions = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel

            // If endIndex is not specified, use all remaining rows
            if (endIndex == -1 || endIndex >= DTUserDetails.Rows.Count)
            {
                endIndex = DTUserDetails.Rows.Count - 1;
            }

            _logger.LogInformation("User Details has {Count} Rows. Creating subscriptions from index {StartIndex} to {EndIndex}",
                DTUserDetails.Rows.Count, startIndex, endIndex);

            for (int i = startIndex; i <= endIndex; i++)
            {
                if (i >= DTUserDetails.Rows.Count) break;

                DataRow DRRow = DTUserDetails.Rows[i];
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".activity\"},");
                ++Counter;

                if (Counter >= maxSubscriptions)
                    break;
            }

            //Console.WriteLine("Activity Subscription for the following agents: {0}", SubscriptionJSON.ToString());


            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ChilKatJsonObj.ReturnJson(URL, JSONBodyString);

            }


        }

        private void CreateUserAdherenceSubs(WebSocketDetail WebSock, int startIndex = 0, int endIndex = -1)
        {
            _logger.LogInformation("Creating Adherence Channel For Users");

            // Create Subscriptions for Notification - Activity and WFM
            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            int maxSubscriptions = MAX_TOPICS_PER_CHANNEL; // Genesys Cloud limit is 1000 topics per channel

            // If endIndex is not specified, use all remaining rows
            if (endIndex == -1 || endIndex >= DTUserDetails.Rows.Count)
            {
                endIndex = DTUserDetails.Rows.Count - 1;
            }

            _logger.LogInformation("Creating subscriptions for users from index {StartIndex} to {EndIndex}", startIndex, endIndex);

            for (int i = startIndex; i <= endIndex; i++)
            {
                if (i >= DTUserDetails.Rows.Count) break;

                DataRow DRRow = DTUserDetails.Rows[i];
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".workforcemanagement.adherence\"},");
                ++Counter;

                if (Counter >= maxSubscriptions)
                    break;
            }
            //Console.WriteLine("Adherence Subscription for the following agents: {0}", SubscriptionJSON.ToString());
            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = "/api/v2/notifications/channels/" + WebSock.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WebSock.id, APIKey.Substring(0, 6));

                string JsonString = ChilKatJsonObj.ReturnJson(URL, JSONBodyString);

            }


        }
        private void TimerCallBack(object sender, ElapsedEventArgs elapsedEventArg)
        {
            try
            {
                if (ShouldExit)
                    return;

                Console.WriteLine("\nProcessing DB Check:{0}", SyncType);

                switch (SyncType)
                {

                    case "userActivity":
                    case "userCalls":
                    case "queueCalls":
                        UserUpdate();
                        break;
                    default:
                        //ConvUpdate();
                        break;
                }
            }
            catch (Exception ex)
            {
                // Exceptions in this callback (from System.Timers.ElapsedEventHandler) are ignored and not logged.
                // Legacy code did not handle this at all, and all exceptions were silently ignored.
                // May need to move out of the timer callback for error handling to work as expected.
                // TODO: Throw here when comfortable all common exceptions have been fixed/handled.
                _logger.LogError(ex, "Suppressed error");
            }
        }

        public DataTable GetUserStatus()
        {
            DataTable Users = DBAdapter.CreateInMemTable("userRealTimeData");

            //DataTable Users = CreateUsersTable();
            int CurrentPage = 1;
            int MaxPages = 30;
            int UserCounter = 0;

            while (CurrentPage <= MaxPages)
            {
                string JsonString = ChilKatJsonObj.ReturnJson("/api/v2/users?state=active&pageSize=500&pageNumber=" + CurrentPage + "&expand=presence%2CroutingStatus%2Cgeolocation%2CconversationSummary&sortOrder=asc");

                UserReal.UserRealTime UserData = new UserReal.UserRealTime();

                UserData = JsonConvert.DeserializeObject<UserReal.UserRealTime>(JsonString,
                       new JsonSerializerSettings
                       {
                           NullValueHandling = NullValueHandling.Ignore
                       });

                MaxPages = UserData.pageCount;

                foreach (UserReal.Entity JSON in UserData.entities)
                {
                    if (UserCounter % 100 == 0)
                        Console.Write("#");

                    DataRow UserRow = Users.Select("id='" + JSON.id + "'").FirstOrDefault();


                    if (UserRow != null)
                        UserRow.AcceptChanges();
                    else
                    {
                        UserRow = Users.NewRow();
                        Console.Write("+");
                    }


                    JSON.routingStatus.startTime = new DateTime(
                                      JSON.routingStatus.startTime.Ticks - (JSON.routingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                      JSON.routingStatus.startTime.Kind
                                    );
                    if (JSON.routingStatus.startTime.Year < 2000)
                        JSON.routingStatus.startTime = DateTime.Parse("2000-01-01");

                    if (UserRow.RowState == DataRowState.Detached || ((string)UserRow["routingstatus"] != JSON.routingStatus.status
                             || (string)UserRow["systempresence"] != JSON.presence.presenceDefinition.systemPresence
                             || (string)UserRow["presenceid"] != JSON.presence.presenceDefinition.id
                             || (DateTime)UserRow["routstarttime"] != JSON.routingStatus.startTime)
                       )
                    {
                        UserRow["id"] = JSON.id;
                        UserRow["name"] = JSON.name;
                        UserRow["email"] = JSON.email;
                        UserRow["jabberId"] = JSON.chat.jabberId;
                        UserRow["state"] = JSON.state;
                        UserRow["title"] = JSON.title;
                        UserRow["username"] = JSON.username;
                        UserRow["department"] = JSON.department;
                        UserRow["routingstatus"] = JSON.routingStatus.status;
                        UserRow["routstarttime"] = JSON.routingStatus.startTime;
                        if (JSON.presence != null)
                        {
                            UserRow["systempresence"] = JSON.presence.presenceDefinition.systemPresence;
                            UserRow["presenceid"] = JSON.presence.presenceDefinition.id;
                            UserRow["presstarttime"] = JSON.presence.modifiedDate;
                        }
                        UserRow["cccallactive"] = JSON.conversationSummary.call.contactCenter.active;
                        UserRow["cccallacw"] = JSON.conversationSummary.call.contactCenter.acw;
                        UserRow["othcallactive"] = JSON.conversationSummary.call.enterprise.active;
                        UserRow["cbcallactive"] = JSON.conversationSummary.callback.contactCenter.active;
                        UserRow["cbcallacw"] = JSON.conversationSummary.callback.contactCenter.acw;
                        UserRow["cbothcallactive"] = JSON.conversationSummary.callback.enterprise.active;
                        UserRow["cccallactive"] = JSON.conversationSummary.call.contactCenter.active;
                        UserRow["cccallacw"] = JSON.conversationSummary.call.contactCenter.acw;
                        UserRow["othcallactive"] = JSON.conversationSummary.call.enterprise.active;
                        UserRow["cbcallactive"] = JSON.conversationSummary.callback.contactCenter.active;
                        UserRow["cbcallacw"] = JSON.conversationSummary.callback.contactCenter.acw;
                        UserRow["cbothcallactive"] = JSON.conversationSummary.callback.enterprise.active;
                        UserRow["ccemailactive"] = JSON.conversationSummary.email.contactCenter.active;
                        UserRow["ccemailacw"] = JSON.conversationSummary.email.contactCenter.acw;
                        UserRow["othemailactive"] = JSON.conversationSummary.email.enterprise.active;
                        UserRow["ccchatactive"] = JSON.conversationSummary.chat.contactCenter.active;
                        UserRow["ccchatacw"] = JSON.conversationSummary.chat.contactCenter.acw;
                        UserRow["othchatactive"] = JSON.conversationSummary.chat.enterprise.active;
                    }



                    if (UserRow.RowState == DataRowState.Detached)
                    {
                        Users.Rows.Add(UserRow);
                    }

                    ++UserCounter;
                }

                CurrentPage++;
            }



            Console.WriteLine("\nWe Have Returned {0} Row(s)", UserCounter);

            return Users;
        }

        private Boolean TransActivity(string JsonString)
        {
            Boolean Successful = false;

            Console.Write("Act:");
            try
            {
                RealUA.Activity UserActivity = new RealUA.Activity();
                UserActivity = JsonConvert.DeserializeObject<RealUA.Activity>(JsonString,
                            new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            });
                RealUA.EventbodyUser RealUserInfo = UserActivity.eventBody;

                lock (DTUserData)
                {
                    DataRow DRUserAct = DTUserData.Select("id = '" + RealUserInfo.id + "'").FirstOrDefault();

                    if (DRUserAct != null)
                    {
                        Console.WriteLine("Act: Updating user data for ID: " + RealUserInfo.id);

                        DRUserAct["routingStatus"] = RealUserInfo.routingStatus.status;
                        DRUserAct["routstarttime"] = RealUserInfo.routingStatus.startTime;
                        DRUserAct["systemPresence"] = RealUserInfo.presence.presenceDefinition.systemPresence;
                        DRUserAct["presenceId"] = RealUserInfo.presence.presenceDefinition.id;
                        DRUserAct["presstarttime"] = RealUserInfo.presence.modifiedDate;
                    }
                    else
                    {
                        Console.WriteLine("Act: No matching user data found for ID: " + RealUserInfo.id);
                    }
                    WriteUserDataAct = true;
                    Successful = true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Act: Error in TransActivity - " + ex.Message);
                Successful = false;
            }
            finally
            {
                Console.WriteLine("Act: Finished TransActivity with Successful status: " + Successful);
            }
            return Successful;
        }

        private Boolean TransUserCallDets(string JsonString)
        {


            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            Boolean Successful = false;
            Console.Write("CllDets:");

            lock (DTUserCallsDets)
            {

                RealCN.Conversations UserConvs = new RealCN.Conversations();
                UserConvs = JsonConvert.DeserializeObject<RealCN.Conversations>(JsonString,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });

                RealCN.Eventbody RealConversation = UserConvs.eventBody;
                Console.Write("Conv={0}:", RealConversation.id.Substring(0, 5));

                foreach (RealCN.Participant ConvPart in RealConversation.participants)
                {
                    if (ConvPart.purpose == "agent" || ConvPart.purpose == "user")
                    {

                        bool RowFound = false;
                        DataRow ConvDetails = DTUserCallsDets.Select("conversationid = '" + RealConversation.id + "' and userid='" + ConvPart.userId + "'").FirstOrDefault();

                        if (ConvDetails == null)
                        {
                            ConvDetails = DTUserCallsDets.NewRow();
                        }
                        else
                        {
                            RowFound = true;
                        }

                        ConvDetails["keyid"] = ConvPart.userId + "|" + RealConversation.id;
                        ConvDetails["userid"] = ConvPart.userId;
                        ConvDetails["conversationid"] = RealConversation.id;
                        ConvDetails["acwstate"] = false;

                        string MediaType = string.Empty;
                        List<RealCN.Interaction> Interact = new List<RealCN.Interaction>();

                        if (ConvPart.calls != null)
                        {
                            Interact.AddRange(ConvPart.calls);
                            MediaType = "voice";
                        }
                        else if (ConvPart.callbacks != null)
                        {
                            Interact.AddRange(ConvPart.callbacks);
                            MediaType = "callback";
                        }
                        else if (ConvPart.emails != null)
                        {
                            Interact.AddRange(ConvPart.emails);
                            MediaType = "email";
                        }
                        else if (ConvPart.chats != null)
                        {
                            Interact.AddRange(ConvPart.chats);
                            MediaType = "chat";
                        }
                        else if (ConvPart.messages != null)
                        {
                            Interact.AddRange(ConvPart.messages);
                            MediaType = "message";
                        }
                        else
                        {
                            Console.WriteLine("Json Not a recognised Call Type\n{0}", JsonString);

                        }

                        if (Interact != null)
                        {
                            foreach (RealCN.Interaction ConvCall in Interact)
                            {
                                ConvDetails["Media"] = MediaType;
                                ConvDetails["Conversationstate"] = ConvCall.state;
                                if (MediaType == "callback")
                                    ConvDetails["Direction"] = "inbound";
                                else
                                    ConvDetails["Direction"] = ConvCall.direction;

                                ConvDetails["actingas"] = ConvPart.purpose;
                                ConvDetails["QueueId"] = ConvPart.queueId;
                                ConvDetails["updated"] = DateTime.UtcNow;

                                if (ConvPart.conversationRoutingData != null && ConvPart.conversationRoutingData.skills != null)
                                {
                                    if (ConvPart.conversationRoutingData.skills.Count() > 0)
                                        ConvDetails["skill1"] = ConvPart.conversationRoutingData.skills[0].id;
                                    if (ConvPart.conversationRoutingData.skills.Count() > 1)
                                        ConvDetails["skill2"] = ConvPart.conversationRoutingData.skills[1].id;
                                    if (ConvPart.conversationRoutingData.skills.Count() > 2)
                                        ConvDetails["skill3"] = ConvPart.conversationRoutingData.skills[2].id;


                                    ConvDetails["initialpriority"] = ConvPart.conversationRoutingData.priority;
                                }



                                if (ConvCall.state == "connected")
                                    if (ConvPart.connectedTime.HasValue == false || ConvPart.connectedTime.Value.Year < 2000)
                                    {
                                        ConvDetails["talktime"] = DBNull.Value;
                                    }
                                    else
                                    {
                                        ConvDetails["talktime"] = ConvPart.connectedTime;
                                        ConvDetails["talktimeltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvPart.connectedTime.Value, AppTimeZone);
                                    }
                                else
                                    ConvDetails["talktime"] = DBNull.Value;

                                if (ConvCall.afterCallWork != null)
                                {
                                    ConvDetails["acwstring"] = ConvCall.afterCallWork.state;
                                    switch (ConvCall.afterCallWork.state)
                                    {
                                        case "pending":
                                            if (ConvCall.afterCallWork.startTime.Year < 2000)
                                                ConvDetails["acwtime"] = DBNull.Value;
                                            else
                                                ConvDetails["acwtime"] = ConvCall.afterCallWork.startTime;
                                            ConvDetails["acwstate"] = true;
                                            break;
                                        case "completed":
                                            ConvDetails["acwtime"] = DBNull.Value;
                                            ConvDetails["acwstate"] = false;
                                            ConvDetails["Conversationstate"] = "terminated";
                                            break;
                                        default:
                                            ConvDetails["acwtime"] = DBNull.Value;
                                            ConvDetails["acwstate"] = false;
                                            break;
                                    }
                                }


                                if (ConvCall.held == false)
                                {
                                    ConvDetails["heldstate"] = ConvCall.held;
                                    ConvDetails["heldtime"] = DBNull.Value;
                                }
                                else
                                {
                                    if (ConvCall.startHoldTime.HasValue == false || ConvCall.startHoldTime.Value.Year < 2000)
                                        ConvDetails["heldtime"] = DBNull.Value;
                                    else
                                        ConvDetails["heldtime"] = ConvCall.startHoldTime.Value;
                                    ConvDetails["heldstate"] = ConvCall.held;
                                }
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Unknown Media Type");
                        }

                        if (RowFound == false)
                        {
                            DTUserCallsDets.Rows.Add(ConvDetails);
                        }
                        else
                        {
                            DTUserCallsDets.AcceptChanges();
                        }

                    }
                }

                WriteUserDataCallsDets = true;


            }

            WriteUserDataCallsDets = true;

            return Successful;
        }

        private Boolean TransQConv(string JsonString)
        {
            Boolean Successful = false;

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            Console.Write("QClDets:");

            lock (DTQueueCallsDets)
            {

                RealCN.Conversations UserConvs = new RealCN.Conversations();
                UserConvs = JsonConvert.DeserializeObject<RealCN.Conversations>(JsonString,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });

                RealCN.Eventbody RealConversation = UserConvs.eventBody;
                Console.Write("Conv={0}:", RealConversation.id.Substring(0, 5));

                foreach (RealCN.Participant ConvPart in RealConversation.participants)
                {
                        if (ConvPart.purpose == "acd" || ConvPart.purpose == "agent")
                    {
                        bool RowFound = false;
                        bool CallConnected = false;
                        DataRow ConvDetails = DTQueueCallsDets.Select("keyid = '" + RealConversation.id + "|" + ConvPart.queueId + "'").FirstOrDefault();

                        if (ConvDetails == null)
                        {
                            ConvDetails = DTQueueCallsDets.NewRow();
                        }
                        else
                        {
                            RowFound = true;
                        }

                        ConvDetails["keyid"] = RealConversation.id + "|" + ConvPart.queueId;
                        ConvDetails["userid"] = ConvPart.userId;
                        ConvDetails["conversationid"] = RealConversation.id;

                        ConvDetails["acwstate"] = false;

                        string MediaType = string.Empty;
                        List<RealCN.Interaction> Interact = new List<RealCN.Interaction>();

                        if (ConvPart.calls != null)
                        {
                            foreach (RealCN.Interaction ConvCall in ConvPart.calls)
                            {
                                if (ConvCall.state == "connected")
                                {
                                    CallConnected = true;
                                }
                            }

                            if (ConvPart.callbacks != null && CallConnected == false)
                            {
                                Interact.AddRange(ConvPart.callbacks);
                                MediaType = "callback";
                            }
                            else
                            {
                                Interact.AddRange(ConvPart.calls);
                                MediaType = "voice";
                            }

                        }
                        else if (ConvPart.callbacks != null)
                        {
                            Interact.AddRange(ConvPart.callbacks);
                            MediaType = "callback";
                        }
                        else if (ConvPart.emails != null)
                        {
                            Interact.AddRange(ConvPart.emails);
                            MediaType = "email";
                        }
                        else if (ConvPart.chats != null)
                        {
                            Interact.AddRange(ConvPart.chats);
                            MediaType = "chat";
                        }
                        else if (ConvPart.messages != null)
                        {
                            Interact.AddRange(ConvPart.messages);
                            MediaType = "message";
                        }
                        else
                        {
                            _logger.LogWarning("Json Not a recognised Call Type\n{0}", JsonString);

                        }

    #nullable enable
                        if (Interact != null)
                        {
                            foreach (RealCN.Interaction ConvCall in Interact)
                            {
                                ConvDetails["Media"] = MediaType;
                                ConvDetails["Conversationstate"] = ConvCall.state;
                                if (MediaType == "callback")
                                    ConvDetails["Direction"] = "inbound";
                                else
                                    ConvDetails["Direction"] = ConvCall.direction;

                                ConvDetails["actingas"] = ConvPart.purpose;
                                ConvDetails["QueueId"] = ConvPart.queueId;
                                ConvDetails["updated"] = DateTime.UtcNow;

                                TimeSpan TMCheck = TimeSpan.Zero;
                                if (ConvPart.connectedTime == null)
                                {
                                    ConvDetails["startdate"] = DateTime.UtcNow;
                                    ConvDetails["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, AppTimeZone);
                                }
                                else
                                {
                                    ConvDetails["startdate"] = ConvCall.connectedTime ?? Convert.DBNull;
                                    if (ConvCall.connectedTime.HasValue)
                                    {
                                        ConvDetails["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(
                                            ConvCall.connectedTime.Value, AppTimeZone);
                                        TMCheck = DateTime.UtcNow - ConvCall.connectedTime.Value;
                                    }
                                    else
                                    {
                                        ConvDetails["startdateltc"] = DBNull.Value;
                                    }
                                }

                                if (TMCheck.TotalDays > 365.00)
                                {
                                    ConvDetails["startdate"] = DateTime.UtcNow;
                                    ConvDetails["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, AppTimeZone);
                                    _logger?.LogDebug("Adjusting Corrupted Date to Avoid Issues - {0} - Total Days {1} Date in Question {2}",
                                        RealConversation.id,
                                        TMCheck.TotalDays,
                                        ConvCall.connectedTime);
                                }
    #nullable restore

                                if (ConvPart.conversationRoutingData != null && ConvPart.conversationRoutingData.skills != null)
                                {
                                    if (ConvPart.conversationRoutingData.skills.Count() > 0)
                                        ConvDetails["skill1"] = ConvPart.conversationRoutingData.skills[0].id;
                                    if (ConvPart.conversationRoutingData.skills.Count() > 1)
                                        ConvDetails["skill2"] = ConvPart.conversationRoutingData.skills[1].id;
                                    if (ConvPart.conversationRoutingData.skills.Count() > 2)
                                        ConvDetails["skill3"] = ConvPart.conversationRoutingData.skills[2].id;

                                    ConvDetails["initialpriority"] = ConvPart.conversationRoutingData.priority;
                                }


                                if (ConvCall.state == "connected")
                                    if (ConvPart.connectedTime == null || ConvPart.connectedTime.Value.Year < 2000)
                                        ConvDetails["talktime"] = DBNull.Value;
                                    else
                                    {
                                        ConvDetails["talktime"] = ConvPart.connectedTime;
                                        ConvDetails["talktimeltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvPart.connectedTime.Value, AppTimeZone);
                                    }
                                else
                                    ConvDetails["talktime"] = DBNull.Value;

                                if (ConvCall.afterCallWork != null)
                                {
                                    ConvDetails["acwstring"] = ConvCall.afterCallWork.state;
                                    switch (ConvCall.afterCallWork.state)
                                    {
                                        case "pending":
                                            if (ConvCall.afterCallWork.startTime.Year < 2000)
                                                ConvDetails["acwtime"] = DBNull.Value;
                                            else
                                                ConvDetails["acwtime"] = ConvCall.afterCallWork.startTime;
                                            ConvDetails["acwstate"] = true;
                                            break;
                                        case "completed":
                                            ConvDetails["acwtime"] = DBNull.Value;
                                            ConvDetails["acwstate"] = false;
                                            ConvDetails["Conversationstate"] = "terminated";
                                            break;
                                        default:
                                            ConvDetails["acwtime"] = DBNull.Value;
                                            ConvDetails["acwstate"] = false;
                                            break;
                                    }
                                }


                                if (ConvCall.held == false)
                                {
                                    ConvDetails["heldstate"] = ConvCall.held;
                                    ConvDetails["heldtime"] = DBNull.Value;
                                }
                                else
                                {
                                    if (ConvCall.startHoldTime == null || ConvCall.startHoldTime.Value.Year < 2000)
                                        ConvDetails["heldtime"] = DBNull.Value;
                                    else
                                        ConvDetails["heldtime"] = ConvCall.startHoldTime;
                                    ConvDetails["heldstate"] = ConvCall.held;

                                }
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Unknown Media Type");
                        }
                        if (RowFound == false)
                        {
                            DTQueueCallsDets.Rows.Add(ConvDetails);
                        }
                        else
                        {
                            DTQueueCallsDets.AcceptChanges();
                        }
                    }
                }

                WriteQueueDataCallsDets = true;


            }

            WriteQueueDataCallsDets = true;

            return Successful;
        }

        private Boolean TransAdherence(string JsonString)
        {
            Boolean Successful = false;

            Console.Write("Adh:");

            RealUA.Adherence UserAdherence = new RealUA.Adherence();
            UserAdherence = JsonConvert.DeserializeObject<RealUA.Adherence>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
            RealUA.EventbodyAdherence RealUserInfo = UserAdherence.eventBody;


            lock (DTUserData)
            {
                DataRow DRUserAct = DTUserData.Select("id = '" + RealUserInfo.user.id + "'").FirstOrDefault();

                if (DRUserAct != null)
                {
                    DRUserAct["id"] = RealUserInfo.user.id;
                    DRUserAct["adherenceState"] = RealUserInfo.adherenceState;
                    DRUserAct["adherencestarttime"] = RealUserInfo.adherenceChangeTime;
                    DRUserAct["impact"] = RealUserInfo.impact;
                    DRUserAct["scheduledActivityCategory"] = RealUserInfo.scheduledActivityCategory;
                }
                WriteUserDataAdh = true;
                Successful = true;
            }


            Successful = true;



            return Successful;
        }

        private Boolean TransCalls(string JsonString)
        {
            Boolean Successful = true;

            Console.Write("Cls:");

            RealUC.CallStats UserCalls = new RealUC.CallStats();
            UserCalls = JsonConvert.DeserializeObject<RealUC.CallStats>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
            RealUC.Eventbody RealUserInfo = UserCalls.eventBody;


            lock (DTUserData)
            {
                DataRow DRUserAct = DTUserData.Select("id = '" + UserCalls.topicName.Split('.')[2] + "'").FirstOrDefault();

                if (DRUserAct != null)
                {
                    DRUserAct["cccallactive"] = RealUserInfo.call.contactCenter.active;
                    DRUserAct["cccallacw"] = RealUserInfo.call.contactCenter.acw;
                    DRUserAct["othcallactive"] = RealUserInfo.call.enterprise.active;
                    DRUserAct["othcallactive"] = RealUserInfo.call.enterprise.acw;
                    DRUserAct["cbcallactive"] = RealUserInfo.callback.contactCenter.active;
                    DRUserAct["cbcallacw"] = RealUserInfo.callback.contactCenter.acw;
                    DRUserAct["cbothcallactive"] = RealUserInfo.callback.enterprise.active;
                    DRUserAct["cbothcallacw"] = RealUserInfo.callback.enterprise.acw;
                    DRUserAct["ccemailactive"] = RealUserInfo.email.contactCenter.active;
                    DRUserAct["ccemailacw"] = RealUserInfo.email.contactCenter.acw;
                    DRUserAct["othemailactive"] = RealUserInfo.email.enterprise.active;
                    DRUserAct["othemailacw"] = RealUserInfo.email.enterprise.acw;
                    DRUserAct["ccchatactive"] = RealUserInfo.chat.contactCenter.active;
                    DRUserAct["ccchatacw"] = RealUserInfo.chat.contactCenter.acw;
                    DRUserAct["othchatactive"] = RealUserInfo.chat.enterprise.active;
                    DRUserAct["othchatacw"] = RealUserInfo.chat.enterprise.acw;
                }
                WriteUserDataCalls = true;
                Successful = true;
            }


            //DataRow DRCallStats = DTUserData.NewRow();

            //DRCallStats["id"] = UserCalls.topicName.Split('.')[2];
            //DRCallStats["id"] = UserCalls.topicName.Split('.')[2];

            //DTUserData.Rows.Add(DRCallStats);

            Console.Write("\nCls{0}Row(s):", DTUserData.Rows.Count);
            Successful = true;

            return Successful;
        }
    }

    public class WebSocketDetail
    {
        public string connectUri { get; set; }
        public string id { get; set; }
        public DateTime expires { get; set; }
        public string ReportName { get; set; }
    }

    public class AlertObject
    {
        public string topicName { get; set; }
        public string version { get; set; }
    }

}
// spell-checker: ignore: convs, chil, acti, crouting, cgeolocation, cconversation
