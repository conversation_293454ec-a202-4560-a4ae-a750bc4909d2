using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateMessageData
    {
        private readonly ILogger? _logger;

        public GCUpdateMessageData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCMessageData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            Boolean Successful = false;
            DateTime Start = DateTime.Now;
            string SyncType = "messagedata";

            GCGetData GCData = new GCGetData(_logger);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

            DataTable GCMessageData = GCData.MessageData(renameParticipantAttributeNames);

            Console.WriteLine("Writing Message Data Rows {0}", GCMessageData.Rows.Count);
            // Write to chatdata table with mediatype='message' for unified storage
            Successful = DBAdapter.WriteSQLData(GCMessageData, "chatdata");

            Console.WriteLine("Update Date is : {0}", GCData.DetailMessageLastUpdate);
            if (GCData.DetailMessageLastUpdate > OldUpdateTime && Successful == true)

            {
                Console.WriteLine("Updating Max Update Date {0}", GCData.DetailMessageLastUpdate);
                OldUpdateTime = GCData.DetailMessageLastUpdate;
            }

            if (Successful == true)
            {
                OriginalTime = OriginalTime.Add(GCData.MaxSpanToSync).AddHours(-2);

                if (OldUpdateTime >= OriginalTime)
                    OriginalTime = OldUpdateTime;

                Successful = GCData.UpdateLastSuccessDate(OriginalTime, "messagedata");


                Console.WriteLine("Next Start Time {0} ", OriginalTime);
                Console.WriteLine("Updated The Latest Update Date Successful {0}", Successful);
            }
            else
                Console.WriteLine("Will Not update the last update date - failure in processing");

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            Console.WriteLine("Finished in {0} Sec(s)", (DateTime.Now - Start).TotalSeconds);

            return Successful;

        }

    }
}
