# {{ .Table.Name }}

## {{ "Description" | lookup }}
{{- if ne .Table.Comment "" }}

{{ .Table.Comment | nl2mdnl }}
{{- end }}
{{- if .Table.Def }}

<details>
<summary><strong>{{ "Table Definition" | lookup }}</strong></summary>

```sql
{{ .Table.Def }}
```

</details>
{{- end }}{{ $len := len .ReferencedTables }}{{ if ne $len 0 }}

## {{ "Referenced Tables" | lookup }}
{{ range $rt := .ReferencedTables }}
- {{ $rt }}{{ end }}
{{- end }}
{{- if ne (len .Table.Labels) 0 }}

## {{ "Labels" | lookup }}

{{ .Table.Labels | label_join }}

{{- end }}

## {{ "Columns" | lookup }}
{{ range $l := .Columns }}
|{{ range $d := $l }} {{ $d | nl2br }} |{{ end }}
{{- end }}

{{ $len := len .Viewpoints }}{{ if ne $len 2 -}}
## {{ "Viewpoints" | lookup }}
{{ range $l := .Viewpoints }}
|{{ range $d := $l }} {{ $d | nl2br }} |{{ end }}
{{- end }}

{{ end -}}
{{ $len := len .Constraints -}}{{ if ne $len 2 -}}
## {{ "Constraints" | lookup }}
{{ range $l := .Constraints }}
|{{ range $d := $l }} {{ $d | nl2br }} |{{ end }}
{{- end }}

{{ end -}}
{{ $len := len .Indexes -}}{{ if ne $len 2 -}}
## {{ "Indexes" | lookup }}
{{ range $l := .Indexes }}
|{{ range $d := $l }} {{ $d | nl2br }} |{{ end }}
{{- end }}

{{ end -}}
{{ $len := len .Triggers -}}{{ if ne $len 2 -}}
## {{ "Triggers" | lookup }}
{{ range $l := .Triggers }}
|{{ range $d := $l }} {{ $d | nl2br }} |{{ end }}
{{- end }}

{{ end -}}
{{- if .er -}}
## {{ "Relations" | lookup }}

{{ .erDiagram }}

{{ end -}}
---
