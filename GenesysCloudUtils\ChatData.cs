using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using StandardUtils;
using System.Text;
using Newtonsoft.Json;
using System.Linq;
using System.Net;
using DBUtils;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    public class ChatData
    {

        public string? CustomerKeyID { get; set; }
        public string? GCApiKey { get; set; }
        public DateTime DetailChatLastUpdate { get; set; }
        public DataSet? GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des? UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string? TimeZoneConfig { get; set; }

        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public ChatData(ILogger logger)
        {
            _logger = logger;

            GCUtilities.Initialize();

            Console.WriteLine("Initialization of GC Chat Data V1.20.26.11.01");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            DBUtil.Initialize();
        }

        public DataTable GetChatDataFromGC(string ConversationId)
        {
            Console.WriteLine("Retrieving Chat Data from {0} ", ConversationId);
            DataTable ChatData = CreateRawChatTable();
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            try
            {
                StringBuilder JsonString = new StringBuilder(JsonActions.JsonReturnString(URI + "/api/v2/conversations/" + ConversationId + "/recordings?formatId=mp3", GCApiKey));

                JsonString.Length = JsonString.Length - 1;
                JsonString.Remove(0, 1);

                ChatObject JSON = new ChatObject();

                JSON = JsonConvert.DeserializeObject<ChatObject>(JsonString.ToString(),
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                if (JSON.transcript != null)
                {
                    foreach (Transcript ConversationLine in JSON.transcript)
                    {
                        //Console.WriteLine("Found Conversation Line");
                        DataRow ChatLine = ChatData.NewRow();
                        ChatLine["conversationid"] = ConversationId;
                        ChatLine["to"] = ConversationLine.to;
                        ChatLine["from"] = ConversationLine.from;
                        ChatLine["utc"] = ConversationLine.utc;
                        ChatLine["keyid"] = ConversationId + ConversationLine.utc.ToString();
                        ChatLine["conversationid"] = ConversationId;
                        ChatData.Rows.Add(ChatLine);
                    }

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error In GenesysCloud.Chat.GetChatDataFromGC :{0} Conversation ID: {1}", ex.ToString(), ConversationId);
            }

            return ChatData;
        }

        public DataTable CalculateChatData(
            string StartDate,
            string EndDate,
            DataTable Users,
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            bool ForSQL = false)
        {

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DateTime TempDetailChatLastUpdate = DetailChatLastUpdate;
            DetailData Segments = new DetailData(_logger, renameParticipantAttributeNames);
            Segments.TimeZoneConfig = TimeZoneConfig;

            DataTable TBLSegments = Segments.GetDetailInteractionDataFromGCQuery(StartDate, EndDate).Tables[0];
            DataTable CalculatedChatData = CreateCalculatedChatTable();

            Console.WriteLine("We have {0} Rows in the Segments", TBLSegments.Rows.Count);

            string LastConversationID = String.Empty;

            foreach (DataRow SegmentRow in TBLSegments.Select("MediaType='chat'"))
            {
                if (SegmentRow["conversationid"].ToString() != LastConversationID)
                {
                    string LastPerson = string.Empty;
                    string EmpOrCust = string.Empty;
                    string EmpName = string.Empty;
                    string EmpGroup = string.Empty;
                    string EmpUserId = string.Empty;
                    string ChatInitiatedBy = string.Empty;
                    Boolean FirstChat = true;

                    int AgentCount = 0;
                    int CustCount = 0;

                    double AgentTime = 0;
                    double AgentMinTime = 10000000;
                    double AgentMaxTime = 0;
                    double CustTime = 0;
                    double CustMinTime = 10000000;
                    double CustMaxTime = 0;
                    double AgentResponseTime = 0;
                    double CustResponseTime = 0;

                    DateTime LastChatDate = DateTime.Now;
                    DataTable ChatData = GetChatDataFromGC(SegmentRow["conversationid"].ToString());

                    foreach (DataRow ChatLine in ChatData.Rows)
                    {
                        //Console.WriteLine("Chat From: {0} Time: {1} ", (String)ChatLine["from"], (String)ChatLine["utc"]);

                        if (LastPerson != ChatLine["from"].ToString())
                        {
                            //Check is this Employee or Customer
                            DataRow GCUser = Users.Select("jabberid='" + ChatLine["from"].ToString().Replace("'", "''") + "'").FirstOrDefault();

                            if (GCUser != null)
                            {
                                EmpOrCust = "EMP";
                                EmpName = (string)GCUser["Name"];
                                EmpUserId = (string)GCUser["id"];

                                if (ForSQL == false)
                                    EmpGroup = (string)GCUser["Group"];
                            }
                            else
                                EmpOrCust = "CUST";

                            switch (EmpOrCust)
                            {
                                case "EMP":
                                    if (FirstChat != true)
                                        AgentResponseTime = ((DateTime.Parse(ChatLine["utc"].ToString()) - LastChatDate).TotalSeconds);
                                    else
                                        AgentResponseTime = ((DateTime.Parse(ChatLine["utc"].ToString()) - (DateTime)SegmentRow["conversationstartdate"]).TotalSeconds);

                                    AgentCount++;
                                    AgentTime = AgentTime + AgentResponseTime;
                                    if (AgentResponseTime > AgentMaxTime)
                                        AgentMaxTime = AgentResponseTime;
                                    if (AgentResponseTime < AgentMinTime)
                                        AgentMinTime = AgentResponseTime;

                                    break;
                                case "CUST":
                                    if (FirstChat != true)
                                        CustResponseTime = ((DateTime.Parse(ChatLine["utc"].ToString()) - LastChatDate).TotalSeconds);
                                    else
                                        CustResponseTime = ((DateTime.Parse(ChatLine["utc"].ToString()) - (DateTime)SegmentRow["conversationstartdate"]).TotalSeconds);

                                    CustCount++;
                                    CustTime = CustTime + CustResponseTime;

                                    if (CustResponseTime > CustMaxTime)
                                        CustMaxTime = CustResponseTime;
                                    if (CustResponseTime < CustMinTime)
                                        CustMinTime = CustResponseTime;
                                    break;

                            }
                            LastPerson = (String)ChatLine["from"];
                            LastChatDate = DateTime.Parse(ChatLine["utc"].ToString());

                            if (FirstChat == true)
                            {
                                ChatInitiatedBy = EmpOrCust;
                                FirstChat = false;
                            }

                        }
                    }

                    if (AgentMinTime >= AgentMaxTime)
                        AgentMinTime = AgentMaxTime;

                    if (CustMinTime >= CustMaxTime)
                        CustMinTime = CustMaxTime;

                    LastConversationID = SegmentRow["conversationid"].ToString();

                    if (AgentCount + CustCount > 0)
                    {
                        DataRow ChatRow = CalculatedChatData.NewRow();

                        ChatRow["keyid"] = (string)SegmentRow["conversationid"];
                        ChatRow["ConversationId"] = (string)SegmentRow["conversationid"];
                        ChatRow["conversationstart"] = (DateTime)SegmentRow["conversationstartdate"];
                        ChatRow["conversationstartltc"] = TimeZoneInfo.ConvertTimeFromUtc((DateTime)SegmentRow["conversationstartdate"], AppTimeZone); ;
                        ChatRow["Name"] = EmpName;
                        ChatRow["userid"] = EmpUserId;
                        if (ForSQL == false)
                            ChatRow["Group"] = EmpGroup;
                        ChatRow["LinkedDate"] = (DateTime)SegmentRow["conversationstartdate"];
                        ChatRow["chatinitiatedby"] = ChatInitiatedBy;
                        ChatRow["agentchatcount"] = AgentCount;
                        ChatRow["agentchattotal"] = AgentTime;
                        ChatRow["agentchatmax"] = AgentMaxTime;
                        ChatRow["agentchatmin"] = AgentMinTime;
                        ChatRow["custchatcount"] = CustCount;
                        ChatRow["custchattotal"] = CustTime;
                        ChatRow["custchatmax"] = CustMaxTime;
                        ChatRow["custchatmin"] = CustMinTime;


                        DateTime MaxDateTest = DateTime.Parse(SegmentRow["conversationstartdate"].ToString()).ToUniversalTime();
                        if (MaxDateTest > DetailChatLastUpdate)
                        {
                            DetailChatLastUpdate = MaxDateTest;
                            Console.Write("@");
                        }

                        CalculatedChatData.Rows.Add(ChatRow);
                    }
                }
            }

            if (ForSQL == true)
            {

                DataTable CalculatedChatDataSQL = CreateCalculatedChatTableSQL();


                foreach (DataRow OldData in CalculatedChatData.Rows)
                {
                    DataRow NewSQLRow = CalculatedChatDataSQL.NewRow();


                    NewSQLRow["keyid"] = UCAUtils.GetStableHashCode(OldData["keyid"].ToString());
                    NewSQLRow["conversationid"] = OldData["ConversationId"];
                    NewSQLRow["conversationstart"] = OldData["ConversationStart"];
                    NewSQLRow["conversationstartltc"] = OldData["ConversationStartltc"];
                    NewSQLRow["userid"] = OldData["userid"];
                    NewSQLRow["chatinitiatedby"] = OldData["chatinitiatedby"];
                    NewSQLRow["agentchatcount"] = OldData["agentchatcount"];
                    NewSQLRow["agentchattotal"] = OldData["agentchattotal"];
                    NewSQLRow["agentchatmax"] = OldData["agentchatmax"];
                    NewSQLRow["agentchatmin"] = OldData["agentchatmin"];
                    NewSQLRow["custchatcount"] = OldData["custchatcount"];
                    NewSQLRow["custchattotal"] = OldData["custchattotal"];
                    NewSQLRow["custchatmax"] = OldData["custchatmax"];
                    NewSQLRow["custchatmin"] = OldData["custchatmin"];
                    // Set mediatype to 'chat' for chat job data
                    NewSQLRow["mediatype"] = "chat";
                    CalculatedChatDataSQL.Rows.Add(NewSQLRow);
                }
                return CalculatedChatDataSQL;
            }
            else
            {
                return CalculatedChatData;

            }
        }

        private DataTable CreateCalculatedChatTable()
        {
            DataTable ChatTable = new DataTable();
            ChatTable.TableName = "ChatCalculated";
            ChatTable.Columns.Add("keyid", typeof(String));
            ChatTable.Columns.Add("ConversationId", typeof(String));
            ChatTable.Columns.Add("Name", typeof(String));
            ChatTable.Columns.Add("userid", typeof(String));
            ChatTable.Columns.Add("Group", typeof(String));
            ChatTable.Columns.Add("ConversationStart", typeof(DateTime));
            ChatTable.Columns.Add("ConversationStartltc", typeof(DateTime));
            ChatTable.Columns.Add("LinkedDate", typeof(DateTime));
            ChatTable.Columns.Add("chatinitiatedby", typeof(String));
            ChatTable.Columns.Add("agentchatcount", typeof(double));
            ChatTable.Columns.Add("agentchattotal", typeof(double));
            ChatTable.Columns.Add("agentchatmax", typeof(double));
            ChatTable.Columns.Add("agentchatmin", typeof(double));
            ChatTable.Columns.Add("custchatcount", typeof(double));
            ChatTable.Columns.Add("custchattotal", typeof(double));
            ChatTable.Columns.Add("custchatmax", typeof(double));
            ChatTable.Columns.Add("custchatmin", typeof(double));
            ChatTable.Columns.Add("updated", typeof(DateTime));
            DataColumn[] key = new DataColumn[1];
            key[0] = ChatTable.Columns[0];
            ChatTable.PrimaryKey = key;
            return ChatTable;
        }

        private DataTable CreateCalculatedChatTableSQL()
        {

            DataTable DTTemp = new DataTable();

            String ChatSQL = String.Empty;


            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    ChatSQL = "SELECT TOP (0) * FROM chatData";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    ChatSQL = "SELECT  * FROM chatData LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            DTTemp = DBUtil.GetSQLTableData(ChatSQL, "chatData").Clone();
            DTTemp.TableName = "chatData";
            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            return DTTemp;

        }

        private DataTable CreateRawChatTable()
        {
            DataTable ChatTable = new DataTable();

            ChatTable.Columns.Add("keyid", typeof(String));
            ChatTable.Columns.Add("conversationId", typeof(String));
            ChatTable.Columns.Add("to", typeof(String));
            ChatTable.Columns.Add("from", typeof(String));
            ChatTable.Columns.Add("utc", typeof(String));
            return ChatTable;

        }

    }

}
// spell-checker: ignore: jabberid, chatinitiatedby
