using GenesysCloudDefLearningModules;
using Newtonsoft.Json;

#nullable enable
namespace CSG.Adapter.Genesys.Schema.V2.Response.Organization
{
    public enum State
    {
        Active,
        Inactive,
        Deleted
    }

    public class Me
    {
        [JsonProperty(Required = Required.Always)]
        public string? id { get; set; }
        [JsonProperty(Required = Required.Always)]
        public string? name { get; set; }
        public string? defaultLanguage { get; set; }
        public string? defaultCountryCode { get; set; }
        public string? thirdPartyOrgName { get; set; }
        public string? domain { get; set; }
        public int? version { get; set; }
        public State? state { get; set; }
        public string? defaultSiteId { get; set; }
        public int? thirdPartyOrgId { get; set; }
        public bool? deletable { get; set; }
        public bool? voicemailEnabled { get; set; }
        public string? productPlatform { get; set; }
        public string? selfUri { get; set; }
        public Dictionary<string, bool?>? features { get; set; }
    }
}
#nullable restore

namespace GenesysCloudDefTeamMembers
{

    public class Rootobject
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefTeamDetailed
{

    public class Teams
    {
        public Entity[] entities { get; set; }
        public string selfUri { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public int memberCount { get; set; }
        public string selfUri { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefOfferedForecast
{

    public class OfferedForeCast
    {
        public Result result { get; set; }
    }

    public class Result
    {
        public DateTime referenceStartDate { get; set; }
        public Planninggroup[] planningGroups { get; set; }
        public int weekNumber { get; set; }
        public int weekCount { get; set; }
    }

    public class Planninggroup
    {
        public string planningGroupId { get; set; }
        public float[] offeredPerInterval { get; set; }
        public float[] averageHandleTimeSecondsPerInterval { get; set; }
    }

}

namespace GenesysCloudDefShortTermForecast
{

    public class Entity
    {
        public string Id { get; set; }
        public string WeekDate { get; set; }
        public int WeekCount { get; set; }
        public bool canUseForScheduling { get; set; }

    }

    public class ShortTermForeCast
    {
        public List<Entity> Entities { get; set; }
    }

}

namespace GenesysCloudDefUserPresenceDetailedJobs
{

    public class UserPresenceDetailJob
    {
        public Userdetail[] userDetails { get; set; }
        public string cursor { get; set; }
        public DateTime dataAvailabilityDate { get; set; }
    }

    public class Userdetail
    {
        public string userId { get; set; }
        public Routingstatus[] routingStatus { get; set; }
        public Primarypresence[] primaryPresence { get; set; }
    }

    public class Routingstatus
    {
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string routingStatus { get; set; }
    }

    public class Primarypresence
    {
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string systemPresence { get; set; }
        public string organizationPresenceId { get; set; }
    }

}

namespace GenesysCloudDefODCampaigns
{

    public class Campaigns
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public int version { get; set; }
        public Contactlist contactList { get; set; }
        public Queue queue { get; set; }
        public string dialingMode { get; set; }
        public Script script { get; set; }
        public Edgegroup edgeGroup { get; set; }
        public string campaignStatus { get; set; }
        public Phonecolumn[] phoneColumns { get; set; }
        public float abandonRate { get; set; }
        public object[] dncLists { get; set; }
        public Callanalysisresponseset callAnalysisResponseSet { get; set; }
        public string callerName { get; set; }
        public string callerAddress { get; set; }
        public int outboundLineCount { get; set; }
        public object[] ruleSets { get; set; }
        public bool skipPreviewDisabled { get; set; }
        public int previewTimeOutSeconds { get; set; }
        public bool singleNumberPreview { get; set; }
        public bool alwaysRunning { get; set; }
        public int noAnswerTimeout { get; set; }
        public int priority { get; set; }
        public object[] contactListFilters { get; set; }
        public Division division { get; set; }
        public string selfUri { get; set; }
    }

    public class Contactlist
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Queue
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Script
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Edgegroup
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Callanalysisresponseset
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Phonecolumn
    {
        public string columnName { get; set; }
        public string type { get; set; }
    }

}

namespace GenesysCloudDefODContactList
{

    public class ContactLists
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string lastUri { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime? dateModified { get; set; }
        public int version { get; set; }
        public Division division { get; set; }
        public string[] columnNames { get; set; }
        public Phonecolumn[] phoneColumns { get; set; }
        public object[] previewModeAcceptedValues { get; set; }
        public bool automaticTimeZoneMapping { get; set; }
        public string selfUri { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Phonecolumn
    {
        public string columnName { get; set; }
        public string type { get; set; }
        public string callableTimeColumn { get; set; }
    }

}

namespace GenesysCloudDefPlanningGroups
{


    public class PlanningGroup
    {
        public Entity[] entities { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public Servicegoaltemplate serviceGoalTemplate { get; set; }
        public Routepath[] routePaths { get; set; }
        public Metadata metadata { get; set; }
        public string selfUri { get; set; }
    }

    public class Servicegoaltemplate
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Metadata
    {
        public int version { get; set; }
        public Modifiedby modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
    }

    public class Modifiedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Routepath
    {
        public Queue queue { get; set; }
        public string mediaType { get; set; }
        public Skill[] skills { get; set; }
        public Language language { get; set; }
    }

    public class Queue
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Language
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Skill
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }


}

namespace GenesysCloudDefServiceGoals
{


    public class ServiceGoal
    {
        public Entity[] entities { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public Servicelevel serviceLevel { get; set; }
        public Averagespeedofanswer averageSpeedOfAnswer { get; set; }
        public Abandonrate abandonRate { get; set; }
        public Metadata metadata { get; set; }
        public Impactoverride impactOverride { get; set; }
        public string selfUri { get; set; }
    }

    public class Servicelevel
    {
        public bool include { get; set; }
        public int percent { get; set; }
        public int seconds { get; set; }
    }

    public class Averagespeedofanswer
    {
        public bool include { get; set; }
        public int seconds { get; set; }
    }

    public class Abandonrate
    {
        public bool include { get; set; }
        public int percent { get; set; }
    }

    public class Metadata
    {
        public int version { get; set; }
        public Modifiedby modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
    }

    public class Modifiedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Impactoverride
    {
        public bool enabled { get; set; }
        public Impact impact { get; set; }
    }

    public class Impact
    {
        public ImpactServicelevel serviceLevel { get; set; }
        public ImpactAveragespeedofanswer averageSpeedOfAnswer { get; set; }
        public ImpactAbandonrate abandonRate { get; set; }
    }

    public class ImpactServicelevel
    {
        public float increaseByPercent { get; set; }
        public float decreaseByPercent { get; set; }
    }

    public class ImpactAveragespeedofanswer
    {
        public float increaseByPercent { get; set; }
        public float decreaseByPercent { get; set; }
    }

    public class ImpactAbandonrate
    {
        public float increaseByPercent { get; set; }
        public float decreaseByPercent { get; set; }
    }
}

namespace GenesysCloudDefInteractionSegments
{

    public class InteractionSegmentStruct
    {
        public Conversation[] conversations { get; set; }
    }

    public class Conversation
    {
        public DateTime conversationEnd { get; set; }
        public string conversationId { get; set; }
        public DateTime conversationStart { get; set; }
        public string[] divisionIds { get; set; }
        public decimal mediaStatsMinConversationMos { get; set; }
        public decimal mediaStatsMinConversationRFactor { get; set; }
        public string originatingDirection { get; set; }
        public Participant[] participants { get; set; }
        public string externalTag { get; set; }
    }

    public class Participant
    {
        public string participantId { get; set; }
        public string participantName { get; set; }
        public string purpose { get; set; }
        public Session[] sessions { get; set; }
        public string userId { get; set; }
        public string externalContactId { get; set; }
    }

    public class Session
    {
        public string[] activeSkillIds { get; set; }
        public int agentBullseyeRing { get; set; }
        public string ani { get; set; }
        public string direction { get; set; }
        public string dnis { get; set; }
        public string edgeId { get; set; }
        public string mediaType { get; set; }
        public string protocolCallId { get; set; }
        public string provider { get; set; }
        public bool recording { get; set; }
        public string remoteNameDisplayable { get; set; }
        public string[] requestedRoutings { get; set; }
        public int routingRing { get; set; }
        public string selectedAgentId { get; set; }
        public string sessionDnis { get; set; }
        public string sessionId { get; set; }
        public string usedRouting { get; set; }
        public Mediaendpointstat[] mediaEndpointStats { get; set; }
        public Metric[] metrics { get; set; }
        public Segment[] segments { get; set; }
        public string peerId { get; set; }
        public string remote { get; set; }
        public Flow flow { get; set; }
        public string flowInType { get; set; }
    }

    public class Flow
    {
        public string endingLanguage { get; set; }
        public string entryReason { get; set; }
        public string entryType { get; set; }
        public string exitReason { get; set; }
        public string flowId { get; set; }
        public string flowName { get; set; }
        public string flowType { get; set; }
        public string flowVersion { get; set; }
        public string startingLanguage { get; set; }
        public string transferTargetAddress { get; set; }
        public string transferTargetName { get; set; }
        public string transferType { get; set; }
        public bool issuedCallback { get; set; }
        public Outcomes[] outcomes { get; set; } 
    }

    public class Outcomes
    {
        public string flowOutcome { get; set; }
        public string flowOutcomeEndTimestamp { get; set; }
        public string flowOutcomeId { get; set; }
        public string flowOutcomeStartTimestamp { get; set; }
        public string flowOutcomeValue { get; set; }
    }

    public class Mediaendpointstat
    {
        public string[] codecs { get; set; }
        public DateTime eventTime { get; set; }
        public int maxLatencyMs { get; set; }
        public float minMos { get; set; }
        public float minRFactor { get; set; }
        public int receivedPackets { get; set; }
        public int discardedPackets { get; set; }
        public int overrunPackets { get; set; }
        public int underrunPackets { get; set; }
    }

    public class Metric
    {
        public DateTime emitDate { get; set; }
        public string name { get; set; }
        public Int64 value { get; set; }
    }

    public class Segment
    {
        public bool conference { get; set; }
        public DateTime segmentEnd { get; set; }
        public DateTime segmentStart { get; set; }
        public string segmentType { get; set; }
        public string disconnectType { get; set; }
        public string queueId { get; set; }
        public string[] requestedRoutingSkillIds { get; set; }
        public int[] sipResponseCodes { get; set; }
        public string wrapUpCode { get; set; }
        public string wrapUpNote { get; set; }
        public string errorCode { get; set; }
        public string destinationConversationId { get; set; }
    }

}

namespace GenesysCloudDefInteractions
{

    public class InteractionDataStruct
    {
        public Result[] results { get; set; }
    }

    public class Result
    {
        public Group group { get; set; }
        public Datum[] data { get; set; }
    }

    public class Group
    {
        public string direction { get; set; }
        public string mediaType { get; set; }
        public string queueId { get; set; }
        public string userId { get; set; }
        public string wrapUpCode { get; set; }
    }

    public class Datum
    {
        public string interval { get; set; }
        public Metric[] metrics { get; set; }
        public View[] views { get; set; }
    }

    public class Metric
    {
        public string metric { get; set; }
        public Stats stats { get; set; }
    }

    public class Stats
    {
        public float max { get; set; }
        public float min { get; set; }
        public int count { get; set; }
        public float sum { get; set; }
        public float current { get; set; }
        public float ratio { get; set; }
        public float numerator { get; set; }
        public float denominator { get; set; }
        public float target { get; set; }
    }

    public class View
    {
        public string name { get; set; }
        public ViewStats stats { get; set; }
    }

    public class ViewStats
    {
        public Int64 max { get; set; }
        public Int64 min { get; set; }
        public Int64 count { get; set; }
        public Int64 sum { get; set; }
    }
}

namespace GenesysCloudDefPresence
{
    public class UserPresenceData
    {
        public Systemtoorganizationmappings systemToOrganizationMappings { get; set; }
        public UserPresenceResults[] results { get; set; }
    }

    public class Systemtoorganizationmappings
    {
        public string[] AVAILABLE { get; set; }
        public string[] AWAY { get; set; }
        public string[] BUSY { get; set; }
        public string[] BREAK { get; set; }
        public string[] MEAL { get; set; }
        public string[] MEETING { get; set; }
        public string[] ON_QUEUE { get; set; }
        public string[] OFFLINE { get; set; }
        public string[] TRAINING { get; set; }
    }

    public class UserPresenceResults
    {
        public Group group { get; set; }
        public Datum[] data { get; set; }
    }

    public class Group
    {
        public string userId { get; set; }
    }

    public class Datum
    {
        public string interval { get; set; }
        public Metric[] metrics { get; set; }
    }

    public class Metric
    {
        public string metric { get; set; }
        public string qualifier { get; set; }
        public Stats stats { get; set; }
    }

    public class Stats
    {
        public int sum { get; set; }
    }
}

namespace GenesysCloudDefParticipantAttrib
{

    public class ParticipantAttributes
    {
        public string id { get; set; }
        public Participant[] participants { get; set; }
        public object[] otherMediaUris { get; set; }
        public string recordingState { get; set; }
        public string selfUri { get; set; }
    }

    public class Participant
    {
        public string id { get; set; }
        public string name { get; set; }
        public string address { get; set; }
        public DateTime startTime { get; set; }
        public DateTime connectedTime { get; set; }
        public string purpose { get; set; }
        public string state { get; set; }
        public string direction { get; set; }
        public bool held { get; set; }
        public bool wrapupRequired { get; set; }
        public Queue queue { get; set; }
        public dynamic attributes { get; set; }
        public string provider { get; set; }
        public bool muted { get; set; }
        public bool confined { get; set; }
        public bool recording { get; set; }
        public string recordingState { get; set; }
        public string ani { get; set; }
        public string dnis { get; set; }
        public DateTime endTime { get; set; }
        public string disconnectType { get; set; }
        public string peer { get; set; }
        public Conversationroutingdata conversationRoutingData { get; set; }
        public string wrapupPrompt { get; set; }
        public User user { get; set; }
        public Script script { get; set; }
        public int wrapupTimeoutMs { get; set; }
        public int alertingTimeoutMs { get; set; }
    }

    public class Queue
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }



    public class Conversationroutingdata
    {
        public Queue1 queue { get; set; }
        public int priority { get; set; }
        public Skill[] skills { get; set; }
        public object[] scoredAgents { get; set; }
    }

    public class Queue1
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Skill
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Script
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

#nullable enable
namespace GenesysCloudDefSurveys
{
    public class SurveyAggregations
    {
        [JsonProperty(Required = Required.Always)]
        public Result[]? results { get; set; }
    }

    public class Result
    {
        [JsonProperty(Required = Required.Always)]
        public Group? group { get; set; }
        public Datum[]? data { get; set; }
    }

    public class Group
    {
        public string? mediaType { get; set; }
        [JsonProperty(Required = Required.Always)]
        public string? surveyId { get; set; }
    }

    public class Datum
    {
        public string? interval { get; set; }
        public Metric[]? metrics { get; set; }
    }

    public class Metric
    {
        public string? metric { get; set; }
        public Stats? stats { get; set; }
    }

    public class Stats
    {
        public int? count { get; set; }
        public int? max { get; set; }
        public int? min { get; set; }
        public int? sum { get; set; }
    }

    public class Survey
    {
        [JsonProperty(Required = Required.Always)]
        public string? id { get; set; }
        [JsonProperty(Required = Required.Always)]
        public Conversation? conversation { get; set; }
        public SurveyForm? surveyForm { get; set; }
        public Agent? agent { get; set; }
        public string? status { get; set; }
        public Queue? queue { get; set; }
        public Answers? answers { get; set; }
        public DateTime? completedDate { get; set; }
        public AgentTeam? agentTeam { get; set; }
        public string? selfUri { get; set; }
    }

    public class Conversation
    {
        [JsonProperty(Required = Required.Always)]
        public string? id { get; set; }
        public string? selfUri { get; set; }
    }

    public class SurveyForm
    {
        public string? id { get; set; }
        public string? name { get; set; }
        public DateTime? modifiedDate { get; set; }
        public bool? published { get; set; }
        public bool? disabled { get; set; }
        public string? contextId { get; set; }
        public string? language { get; set; }
        public string? header { get; set; }
        public string? footer { get; set; }
        public QuestionGroup[]? questionGroups { get; set; }
        public string? selfUri { get; set; }

        public QuestionGroup? GetQuestionGroup(string? questionGroupId)
        {
            if (string.IsNullOrEmpty(questionGroupId) || questionGroups is null)
                return null;

            foreach (QuestionGroup group in questionGroups)
            {
                if (group.id == questionGroupId)
                    return group;
            }
            return null;
        }
    }

    public class QuestionGroup
    {
        public string? id { get; set; }
        public string? name { get; set; }
        public string? type { get; set; }
        public bool? naEnabled { get; set; }
        public Question[]? questions { get; set; }

        public Question? GetQuestion(string? questionId)
        {
            if (string.IsNullOrEmpty(questionId) || questions is null)
                return null;

            foreach (Question question in questions)
            {
                if (question.id == questionId)
                    return question;
            }
            return null;
        }
    }

    public class Question
    {
        public string? id { get; set; }
        public string? text { get; set; }
        public string? helpText { get; set; }
        public string? type { get; set; }
        public bool? naEnabled { get; set; }
        public AnswerOption[]? answerOptions { get; set; }

        public AnswerOption? GetAnswerOption(string? answerOptionId)
        {
            if (string.IsNullOrEmpty(answerOptionId) || answerOptions is null)
                return null;

            foreach (AnswerOption option in answerOptions)
            {
                if (option.id == answerOptionId)
                    return option;
            }
            return null;
        }
    }

    public class AnswerOption
    {
        public string? id { get; set; }
        public string? text { get; set; }
        public int? value { get; set; }
    }

    public class Agent
    {
        [JsonProperty(Required = Required.Always)]
        public string? id { get; set; }
        public string? name { get; set; }
        public string? selfUri { get; set; }
    }

    public class Queue
    {
        [JsonProperty(Required = Required.Always)]
        public string? id { get; set; }
        public string? selfUri { get; set; }
    }

    public class Answers
    {
        public decimal? totalScore { get; set; }
        public QuestionGroupScore[]? questionGroupScores { get; set; }
    }

    public class QuestionGroupScore
    {
        public string? questionGroupId { get; set; }
        public decimal? totalScore { get; set; }
        public decimal? maxTotalScore { get; set; }
        public bool? markedNA { get; set; }
        public QuestionScore[]? questionScores { get; set; }
    }

    public class QuestionScore
    {
        public string? questionId { get; set; }
        public string? answerId { get; set; }
        public int? score { get; set; }
        public bool? markedNA { get; set; }
        public string? freeTextAnswer { get; set; }
    }

    public class AgentTeam
    {
        [JsonProperty(Required = Required.Always)]
        public string? id { get; set; }
        public string? selfUri { get; set; }
    }
}
#nullable restore

namespace GenesysCloudDefEvaluations
{

    public class EvaluationAggregations
    {
        public Result[] results { get; set; }
    }

    public class Result
    {
        public Group group { get; set; }
        public Datum[] data { get; set; }
    }

    public class Group
    {
        public string userId { get; set; }
        public string queueId { get; set; }
    }

    public class Datum
    {
        public string interval { get; set; }
        public Metric[] metrics { get; set; }
    }

    public class Metric
    {
        public string metric { get; set; }
        public Stats stats { get; set; }
    }

    public class Stats
    {
        public int count { get; set; }
        public int max { get; set; }
        public int min { get; set; }
        public int sum { get; set; }
    }


    public class Evaluators
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public string nextUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public Evaluator evaluator { get; set; }
        public int numEvaluationsAssigned { get; set; }
        public int numEvaluationsStarted { get; set; }
        public int numEvaluationsCompleted { get; set; }
        public int numCalibrationsAssigned { get; set; }
        public int numCalibrationsStarted { get; set; }
        public int numCalibrationsCompleted { get; set; }
    }

    public class Evaluator
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public Chat chat { get; set; }
        public string department { get; set; }
        public string email { get; set; }
        public Primarycontactinfo[] primaryContactInfo { get; set; }
        public Address[] addresses { get; set; }
        public string state { get; set; }
        public string title { get; set; }
        public string username { get; set; }
        public int version { get; set; }
        public bool acdAutoAnswer { get; set; }
        public string selfUri { get; set; }
        public Image[] images { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Chat
    {
        public string jabberId { get; set; }
    }

    public class Primarycontactinfo
    {
        public string address { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
        public string display { get; set; }
        public string extension { get; set; }
    }

    public class Address
    {
        public string address { get; set; }
        public string display { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
        public string extension { get; set; }
    }

    public class Image
    {
        public string resolution { get; set; }
        public string imageUri { get; set; }
    }


    public class EvaluationOverview
    {
        public EvaluationItem[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public string nextUri { get; set; }
        public int pageCount { get; set; }
    }

    public class EvaluationItem
    {
        public string id { get; set; }
        public Conversation conversation { get; set; }
        public Evaluationform evaluationForm { get; set; }
        public EvaluatorDetails evaluator { get; set; }

        public Calibration calibration { get; set; }
        public Agent agent { get; set; }
        public string status { get; set; }
        public bool agentHasRead { get; set; }
        public DateTime releaseDate { get; set; }
        public DateTime assignedDate { get; set; }
        public DateTime changedDate { get; set; }
        public string[] mediaType { get; set; }
        public DateTime conversationDate { get; set; }
        public DateTime conversationEndDate { get; set; }
        public bool neverRelease { get; set; }
        public string selfUri { get; set; }
    }

    public class Calibration
    {
        public string id { get; set; }
        public decimal averageScore { get; set; }
        public decimal highScore { get; set; }
        public decimal lowScore { get; set; }
        public string selfUri { get; set; }
    }
    public class Conversation
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Evaluationform
    {
        public string id { get; set; }
        public string name { get; set; }
        public bool published { get; set; }
        public string selfUri { get; set; }
    }

    public class EvaluatorDetails
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Agent
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class EvaluationDetails
    {
        public string id { get; set; }
        public Conversation conversation { get; set; }
        public Evaluationform evaluationForm { get; set; }
        public EvaluatorDetails evaluator { get; set; }
        public Agent agent { get; set; }
        public Calibration calibration { get; set; }
        public string status { get; set; }
        public Answers answers { get; set; }
        public bool agentHasRead { get; set; }
        public DateTime releaseDate { get; set; }
        public DateTime assignedDate { get; set; }
        public DateTime changedDate { get; set; }
        public string[] mediaType { get; set; }
        public DateTime conversationDate { get; set; }
        public DateTime conversationEndDate { get; set; }
        public bool neverRelease { get; set; }
        public string selfUri { get; set; }
    }

    public class ConversationDetail
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class EvaluationformDetail
    {
        public string id { get; set; }
        public string name { get; set; }
        public bool published { get; set; }
        public string selfUri { get; set; }
    }


    public class Answers
    {
        public decimal totalScore { get; set; }
        public decimal totalCriticalScore { get; set; }
        public decimal totalNonCriticalScore { get; set; }
        public Questiongroupscore[] questionGroupScores { get; set; }
        public bool anyFailedKillQuestions { get; set; }
        public string comments { get; set; }
    }

    public class Questiongroupscore
    {
        public string questionGroupId { get; set; }
        public decimal totalScore { get; set; }
        public decimal maxTotalScore { get; set; }
        public bool markedNA { get; set; }
        public decimal totalCriticalScore { get; set; }
        public decimal maxTotalCriticalScore { get; set; }
        public decimal totalNonCriticalScore { get; set; }
        public decimal maxTotalNonCriticalScore { get; set; }
        public decimal totalScoreUnweighted { get; set; }
        public decimal maxTotalScoreUnweighted { get; set; }
        public decimal totalCriticalScoreUnweighted { get; set; }
        public decimal maxTotalCriticalScoreUnweighted { get; set; }
        public decimal totalNonCriticalScoreUnweighted { get; set; }
        public decimal maxTotalNonCriticalScoreUnweighted { get; set; }
        public Questionscore[] questionScores { get; set; }
    }

    public class Questionscore
    {
        public string questionId { get; set; }
        public string answerId { get; set; }
        public int score { get; set; }
        public bool markedNA { get; set; }
        public bool failedKillQuestion { get; set; }
        public string comments { get; set; }
    }


}

namespace GenesysCloudDefUserRealtime
{

    public class UserRealTime
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public Chat chat { get; set; }
        public string department { get; set; }
        public string email { get; set; }
        public Primarycontactinfo[] primaryContactInfo { get; set; }
        public Address[] addresses { get; set; }
        public string state { get; set; }
        public string title { get; set; }
        public string username { get; set; }
        public Manager manager { get; set; }
        public Image[] images { get; set; }
        public int version { get; set; }
        public Routingstatus routingStatus { get; set; }
        public Presence presence { get; set; }
        public Conversationsummary conversationSummary { get; set; }
        public bool acdAutoAnswer { get; set; }
        public string selfUri { get; set; }
        public Geolocation geolocation { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Chat
    {
        public string jabberId { get; set; }
    }

    public class Manager
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Routingstatus
    {
        public string status { get; set; }
        public DateTime startTime { get; set; }
    }

    public class Presence
    {
        public string source { get; set; }
        public Presencedefinition presenceDefinition { get; set; }
        public string message { get; set; }
        public DateTime modifiedDate { get; set; }
    }

    public class Presencedefinition
    {
        public string id { get; set; }
        public string systemPresence { get; set; }
        public string selfUri { get; set; }
    }

    public class Conversationsummary
    {
        public Call call { get; set; }
        public Callback callback { get; set; }
        public Email email { get; set; }
        public Message message { get; set; }
        public Chat1 chat { get; set; }
        public Socialexpression socialExpression { get; set; }
        public Video video { get; set; }
    }

    public class Call
    {
        public Contactcenter contactCenter { get; set; }
        public Enterprise enterprise { get; set; }
    }

    public class Contactcenter
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise
    {
        public int active { get; set; }
    }

    public class Callback
    {
        public Contactcenter1 contactCenter { get; set; }
        public Enterprise1 enterprise { get; set; }
    }

    public class Contactcenter1
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise1
    {
        public int active { get; set; }
    }

    public class Email
    {
        public Contactcenter2 contactCenter { get; set; }
        public Enterprise2 enterprise { get; set; }
    }

    public class Contactcenter2
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise2
    {
        public int active { get; set; }
    }

    public class Message
    {
        public Contactcenter3 contactCenter { get; set; }
        public Enterprise3 enterprise { get; set; }
    }

    public class Contactcenter3
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise3
    {
        public int active { get; set; }
    }

    public class Chat1
    {
        public Contactcenter4 contactCenter { get; set; }
        public Enterprise4 enterprise { get; set; }
    }

    public class Contactcenter4
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise4
    {
        public int active { get; set; }
    }

    public class Socialexpression
    {
        public Contactcenter5 contactCenter { get; set; }
        public Enterprise5 enterprise { get; set; }
    }

    public class Contactcenter5
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise5
    {
        public int active { get; set; }
    }

    public class Video
    {
        public Contactcenter6 contactCenter { get; set; }
        public Enterprise6 enterprise { get; set; }
    }

    public class Contactcenter6
    {
        public int active { get; set; }
        public int acw { get; set; }
    }

    public class Enterprise6
    {
        public int active { get; set; }
    }

    public class Geolocation
    {
        public string id { get; set; }
        public string country { get; set; }
        public string region { get; set; }
        public string city { get; set; }
    }

    public class Primarycontactinfo
    {
        public string address { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
        public string display { get; set; }
    }

    public class Address
    {
        public string display { get; set; }
        public string mediaType { get; set; }
        public string countryCode { get; set; }
        public string address { get; set; }
        public string type { get; set; }
    }

    public class Image
    {
        public string resolution { get; set; }
        public string imageUri { get; set; }
    }

}

namespace GenesysCloudDefQueueRealtime
{

    public class QueueRealTime
    {
        public Systemtoorganizationmappings systemToOrganizationMappings { get; set; }
        public Result[] results { get; set; }
    }

    public class Systemtoorganizationmappings
    {
        public string[] OFFLINE { get; set; }
        public string[] MEAL { get; set; }
        public string[] ON_QUEUE { get; set; }
        public string[] AVAILABLE { get; set; }
        public string[] BUSY { get; set; }
    }

    public class Result
    {
        public Group group { get; set; }
        public Datum[] data { get; set; }
    }

    public class Group
    {
        public string queueId { get; set; }
        public string mediaType { get; set; }
    }

    public class Datum
    {
        public string metric { get; set; }
        public Stats stats { get; set; }
        public bool truncated { get; set; }
        public Observation[] observations { get; set; }
        public string qualifier { get; set; }
    }

    public class Stats
    {
        public int count { get; set; }
    }

    public class Observation
    {
        public DateTime observationDate { get; set; }
        public string conversationId { get; set; }
        public string sessionId { get; set; }
        public int routingPriority { get; set; }
        public string participantName { get; set; }
        public string userId { get; set; }
        public string direction { get; set; }
        public string addressFrom { get; set; }
        public string addressTo { get; set; }
        public string[] requestedRoutings { get; set; }
        public string usedRouting { get; set; }
    }

}

namespace GenesysCloudDefVoiceRecMetadata
{



    public class VoiceRecMetadata
    {
        public string id { get; set; }
        public string conversationId { get; set; }
        public string media { get; set; }
        public string fileState { get; set; }
        public int maxAllowedRestorationsForOrg { get; set; }
        public int remainingRestorationsAllowedForOrg { get; set; }
        public string sessionId { get; set; }
        public string selfUri { get; set; }
    }



    public class VoiceRecDetails
    {
        public string id { get; set; }
        public string conversationId { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string media { get; set; }
        public string fileState { get; set; }
        public Mediauris mediaUris { get; set; }
        public int actualTranscodeTimeMs { get; set; }
        public int maxAllowedRestorationsForOrg { get; set; }
        public int remainingRestorationsAllowedForOrg { get; set; }
        public string sessionId { get; set; }
        public string selfUri { get; set; }
    }

    public class Mediauris
    {
        public S S { get; set; }
    }

    public class S
    {
        public string mediaUri { get; set; }
    }




}

namespace GenesysCloudDefDetailedInteractions
{

    public class ReportJob
    {
        public string jobId { get; set; }
    }


    public class ReportJobStatus
    {
        public string state { get; set; }
        public DateTime expirationDate { get; set; }
        public DateTime submissionDate { get; set; }
    }


    public class DetailedInteractions
    {
        public string cursor { get; set; }
        public DateTime dataAvailabilityDate { get; set; }
        public Conversation[] conversations { get; set; }
    }

    public class Conversation
    {
        public string conversationId { get; set; }
        public DateTime conversationStart { get; set; }
        public DateTime conversationEnd { get; set; }
        public decimal mediaStatsMinConversationMos { get; set; }
        public decimal mediaStatsMinConversationRFactor { get; set; }
        public string originatingDirection { get; set; }
        public string externalTag { get; set; }
        public string[] divisionIds { get; set; }
        public Participant[] participants { get; set; }
        public Evaluation[] evaluations { get; set; }
    }

    public class Participant
    {
        public string participantId { get; set; }
        public string userId { get; set; }
        public string purpose { get; set; }
        public Session[] sessions { get; set; }
        public dynamic attributes { get; set; }
        public string participantName { get; set; }
        public string externalContactId { get; set; }
        public string externalOrganizationId { get; set; }
    }


    public class Session
    {
        public string mediaType { get; set; }
        public string sessionId { get; set; }
        public string ani { get; set; }
        public string direction { get; set; }
        public string dnis { get; set; }
        public string sessionDnis { get; set; }
        public string edgeId { get; set; }
        public string peerId { get; set; }
        public Segment[] segments { get; set; }
        public Metric[] metrics { get; set; }
        public Mediaendpointstat[] mediaEndpointStats { get; set; }
        public string protocolCallId { get; set; }
        public string provider { get; set; }
        public string remote { get; set; }
        public string remoteNameDisplayable { get; set; }
        public bool recording { get; set; }
        public Flow flow { get; set; }
        public string callbackUserName { get; set; }
        public string[] callbackNumbers { get; set; }
        public string scriptId { get; set; }
        public bool skipEnabled { get; set; }
        public int timeoutSeconds { get; set; }
        public string flowOutType { get; set; }
        public string roomId { get; set; }
        public DateTime callbackScheduledTime { get; set; }
    }

    public class Flow
    {
        public string flowId { get; set; }
        public string flowName { get; set; }
        public string flowVersion { get; set; }
        public string flowType { get; set; }
        public string exitReason { get; set; }
        public string entryReason { get; set; }
        public string entryType { get; set; }
        public string transferType { get; set; }
        public string transferTargetName { get; set; }
        public string transferTargetAddress { get; set; }
        public bool issuedCallback { get; set; }
        public string startingLanguage { get; set; }
        public string endingLanguage { get; set; }
        public Outcomes[] outcomes { get; set; } 
    }

    public class Outcomes
    {
        public string flowOutcome { get; set; }
        public string flowOutcomeEndTimestamp { get; set; }
        public string flowOutcomeId { get; set; }
        public string flowOutcomeStartTimestamp { get; set; }
        public string flowOutcomeValue { get; set; }
    }

    public class Segment
    {
        public DateTime segmentStart { get; set; }
        public DateTime segmentEnd { get; set; }
        public string queueId { get; set; }
        public string segmentType { get; set; }
        public string destinationConversationId { get; set; }
        public bool conference { get; set; }
        public string disconnectType { get; set; }
        public string wrapUpCode { get; set; }
        public string wrapUpNote { get; set; }
        public string[] requestedRoutingSkillIds { get; set; }
        public int[] sipResponseCodes { get; set; }
        public int[] q850ResponseCodes { get; set; }
        public string errorCode { get; set; }
        public string requestedLanguageId { get; set; }
    }

    public class Metric
    {
        public string name { get; set; }
        public Int64 value { get; set; }
        public DateTime emitDate { get; set; }
    }

    public class Mediaendpointstat
    {
        public string[] codecs { get; set; }
        public float minMos { get; set; }
        public float minRFactor { get; set; }
        public int maxLatencyMs { get; set; }
        public int receivedPackets { get; set; }
        public int discardedPackets { get; set; }
        public int overrunPackets { get; set; }
        public int invalidPackets { get; set; }
        public int duplicatePackets { get; set; }
    }

    public class Evaluation
    {
        public string calibrationId { get; set; }
        public bool deleted { get; set; }
        public string contextId { get; set; }
        public string evaluationId { get; set; }
        public string evaluatorId { get; set; }
        public DateTime eventTime { get; set; }
        public string formId { get; set; }
        public string formName { get; set; }
        public string queueId { get; set; }
        public bool released { get; set; }
        public bool rescored { get; set; }
        public string userId { get; set; }
        public float oTotalCriticalScore { get; set; }
        public float oTotalScore { get; set; }

    }

}

namespace GenesysCloudDefDetailedPresence
{


    public class DetailedPresenceInfo
    {
        public Userdetail[] userDetails { get; set; }
    }

    public class Userdetail
    {
        public string userId { get; set; }
        public Routingstatus[] routingStatus { get; set; }
        public Primarypresence[] primaryPresence { get; set; }
    }

    public class Routingstatus
    {
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string routingStatus { get; set; }
    }

    public class Primarypresence
    {
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string systemPresence { get; set; }
        public string organizationPresenceId { get; set; }
    }

}

namespace GenesysCloudDefWFMSchedule
{

    public class WFMSchedule
    {
        public string status { get; set; }
        public string operationId { get; set; }
        public Result result { get; set; }
    }

    public class Result
    {
        public Agentschedule[] agentSchedules { get; set; }
        public string businessUnitTimeZone { get; set; }
        public Publishedschedule[] publishedSchedules { get; set; }
    }

    public class Agentschedule
    {
        public User user { get; set; }
        public Shift[] shifts { get; set; }
        public Fulldaytimeoffmarker[] fullDayTimeOffMarkers { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Shift
    {
        public string id { get; set; }
        public DateTime startDate { get; set; }
        public int lengthMinutes { get; set; }
        public Activity[] activities { get; set; }
        public bool manuallyEdited { get; set; }
        public Schedule schedule { get; set; }
    }

    public class Schedule
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public string selfUri { get; set; }
    }

    public class Activity
    {
        public DateTime startDate { get; set; }
        public int lengthMinutes { get; set; }
        public string description { get; set; }
        public string activityCodeId { get; set; }
        public bool paid { get; set; }
    }

    public class Fulldaytimeoffmarker
    {
        public string businessUnitDate { get; set; }
        public int lengthMinutes { get; set; }
        public string description { get; set; }
        public string activityCodeId { get; set; }
        public bool paid { get; set; }
        public string timeOffRequestId { get; set; }
    }

    public class Publishedschedule
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public int weekCount { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefAdherence
{

    public class AdherenceData
    {
        public string managementUnitId { get; set; }
        public Users[] userResults { get; set; }
        public Lookupidtosecondarypresenceid lookupIdToSecondaryPresenceId { get; set; }
        public float version { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
    }

    public class Lookupidtosecondarypresenceid
    {
        public string _0 { get; set; }
        public string _1 { get; set; }
        public string _2 { get; set; }
        public string _3 { get; set; }
        public string _4 { get; set; }
        public string _5 { get; set; }
        public string _6 { get; set; }
        public string _7 { get; set; }
        public string _8 { get; set; }
        public string _9 { get; set; }
    }

    public class Users
    {
        public string userId { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public float adherencePercentage { get; set; }
        public float conformancePercentage { get; set; }
        public string impact { get; set; }
        public Exceptioninfo[] exceptionInfo { get; set; }
        public Daymetric[] dayMetrics { get; set; }
        public DateTime actualsEndDate { get; set; }
        public Actual[] actuals { get; set; }
    }

    public class Exceptioninfo
    {
        public int startOffsetSeconds { get; set; }
        public int endOffsetSeconds { get; set; }
        public string scheduledActivityCategory { get; set; }
        public string actualActivityCategory { get; set; }
        public string systemPresence { get; set; }
        public string routingStatus { get; set; }
        public string impact { get; set; }
    }

    public class Daymetric
    {
        public int dayStartOffsetSeconds { get; set; }
        public int adherenceScheduleSeconds { get; set; }
        public int conformanceScheduleSeconds { get; set; }
        public int conformanceActualSeconds { get; set; }
        public int exceptionCount { get; set; }
        public int exceptionDurationSeconds { get; set; }
        public int impactSeconds { get; set; }
        public int scheduleLengthSeconds { get; set; }
        public int actualLengthSeconds { get; set; }
        public float adherencePercentage { get; set; }
        public float conformancePercentage { get; set; }
    }

    public class Actual
    {
        public string actualActivityCategory { get; set; }
        public int startOffsetSeconds { get; set; }
        public int endOffsetSeconds { get; set; }
    }

}

namespace GenesysCloudDefManagementUnit
{


    public class ManagementUnits
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public int pageCount { get; set; }
        public string lastUri { get; set; }
        public string selfUri { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public Businessunit businessUnit { get; set; }
        public string selfUri { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Businessunit
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }




    public class ManagementUnit
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public Businessunit businessUnit { get; set; }
        public string startDayOfWeek { get; set; }
        public string timeZone { get; set; }
        public Settings settings { get; set; }
        public Metadata1 metadata { get; set; }
        public int version { get; set; }
        public Modifiedby2 modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
        public string selfUri { get; set; }
    }

     public class MUAgents
    {
        public List<MUAgent> entities { get; set; }
    }

    public class MUAgent
    {
        public string id { get; set; }
        public AgentUser user { get; set; }
    }

    public class AgentUser
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }


    public class Settings
    {
        public Adherence adherence { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Adherence
    {
        public int severeAlertThresholdMinutes { get; set; }
        public int adherenceTargetPercent { get; set; }
        public int adherenceExceptionThresholdSeconds { get; set; }
        public bool nonOnQueueActivitiesEquivalent { get; set; }
        public bool trackOnQueueActivity { get; set; }
        public Ignoredactivitycategories ignoredActivityCategories { get; set; }
    }

    public class Ignoredactivitycategories
    {
        public string[] values { get; set; }
    }

    public class Metadata
    {
        public int version { get; set; }
        public Modifiedby modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
    }

    public class Modifiedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Metadata1
    {
        public int version { get; set; }
        public Modifiedby1 modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
    }

    public class Modifiedby1
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Modifiedby2
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefTimeOffRequests
{

    public class TimeOff
    {
        public Timeoffrequest[] timeOffRequests { get; set; }
    }

    public class Timeoffrequest
    {
        public string id { get; set; }
        public User user { get; set; }
        public bool isFullDayRequest { get; set; }
        public bool markedAsRead { get; set; }
        public string activityCodeId { get; set; }
        public string status { get; set; }
        public DateTime[] partialDayStartDateTimes { get; set; }
        public string[] fullDayManagementUnitDates { get; set; }
        public int dailyDurationMinutes { get; set; }
        public string notes { get; set; }
        public Submittedby submittedBy { get; set; }
        public DateTime submittedDate { get; set; }
        public Reviewedby reviewedBy { get; set; }
        public DateTime reviewedDate { get; set; }
        public Modifiedby modifiedBy { get; set; }
        public DateTime modifiedDate { get; set; }
        public Metadata metadata { get; set; }
        public string selfUri { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Submittedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Reviewedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Modifiedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Metadata
    {
        public int version { get; set; }
        public Modifiedby1 modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
    }

    public class Modifiedby1
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefManagementUnitUsers
{


    public class ManagementUnit
    {
        public Entity[] entities { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefCallSummary
{

    public class CallSummary
    {
        public Result[] results { get; set; }
    }

    public class Result
    {
        public Group group { get; set; }
        public Datum[] data { get; set; }
    }

    public class Group
    {
        public string conversationId { get; set; }
        public string direction { get; set; }
        public string mediaType { get; set; }
        public string queueId { get; set; }
    }

    public class Datum
    {
        public string interval { get; set; }
        public Metric[] metrics { get; set; }
    }

    public class Metric
    {
        public string metric { get; set; }
        public Stats stats { get; set; }
    }

    public class Stats
    {
        public int count { get; set; }
        public int max { get; set; }
        public int min { get; set; }
        public int sum { get; set; }
        public float ratio { get; set; }
        public int numerator { get; set; }
        public int denominator { get; set; }
        public float target { get; set; }
        public float current { get; set; }
    }

}

namespace GenesysCloudDefSubscriptions
{


    public class SubscriptionDataJob
    {
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public string status { get; set; }
        public Usage[] usages { get; set; }
        public string selfUri { get; set; }
    }

    public class Usage
    {
        public string name { get; set; }
        public Resource[] resources { get; set; }
    }

    public class Resource
    {
        public string name { get; set; }
        public DateTime date { get; set; }
    }


}

namespace GenesysCloudDefSubscriptionOverView
{

    public class SubOverview
    {
        public string currency { get; set; }
        public string[] enabledProducts { get; set; }
        public string subscriptionType { get; set; }
        public Usage[] usages { get; set; }
        public bool isInRampPeriod { get; set; }
        public DateTime rampPeriodStartingTimestamp { get; set; }
        public DateTime rampPeriodEndingTimestamp { get; set; }
        public string minimumMonthlyAmount { get; set; }
        public string selfUri { get; set; }
    }

    public class Usage
    {
        public string name { get; set; }
        public string partNumber { get; set; }
        public string grouping { get; set; }
        public string unitOfMeasureType { get; set; }
        public string usageQuantity { get; set; }
        public string overagePrice { get; set; }
        public bool isCancellable { get; set; }
        public string bundleQuantity { get; set; }
        public bool isThirdParty { get; set; }
        public string prepayQuantity { get; set; }
        public string prepayPrice { get; set; }
        public string usageNotes { get; set; }
        public DateTime startdate{get; set;}
        public DateTime enddate {get; set;}
    }

}

namespace GenesysCloudDefSubUsersUsage
{
    public class UserUsageTask
    {
        public string id { get; set; }
        public string name { get; set; }
        public string status { get; set; }
        public string taskType { get; set; }
        public string selfUri { get; set; }
    }



    public class UserUsageJob
    {
        public string id { get; set; }
        public string name { get; set; }
        public string status { get; set; }
        public string taskType { get; set; }
        public string resultDownloadUrl { get; set; }
        public string selfUri { get; set; }
    }


}

namespace GenesysCloudDefOauthUsage
{

    public class OauthUsageReport
    {
        public string executionId { get; set; }
        public string resultsUri { get; set; }
    }



    public class OauthUsageResult
    {
        public Result[] results { get; set; }
        public string queryStatus { get; set; }
    }

    public class Result
    {
        public string clientId { get; set; }
        public string clientName { get; set; }
        public string organizationId { get; set; }
        public string userId { get; set; }
        public string templateUri { get; set; }
        public string httpMethod { get; set; }
        public int status200 { get; set; }
        public int status300 { get; set; }
        public int status400 { get; set; }
        public int status500 { get; set; }
        public int status429 { get; set; }
        public int requests { get; set; }
        public DateTime date { get; set; }
    }


}

namespace GenesysCloudDefSkillDetail
{

    public class Skills
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public DateTime dateModified { get; set; }
        public string state { get; set; }
        public string version { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefSkillMapping
{

    public class SkillMap
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public float proficiency { get; set; }
        public string state { get; set; }
        public string skillUri { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefScheduleDetailsMapping
{


    public class Schedules
    {
        public Entity[] entities { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public int weekCount { get; set; }
        public string description { get; set; }
        public bool published { get; set; }
        public Shorttermforecast shortTermForecast { get; set; }
        public Generationresults generationResults { get; set; }
        public Metadata metadata { get; set; }
        public string selfUri { get; set; }
    }

    public class Shorttermforecast
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public string selfUri { get; set; }
    }

    public class Generationresults
    {
        public bool failed { get; set; }
        public string runId { get; set; }
        public int messageCount { get; set; }
    }

    public class Metadata
    {
        public int version { get; set; }
        public Modifiedby modifiedBy { get; set; }
        public DateTime dateModified { get; set; }
    }

    public class Modifiedby
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }


}

namespace GenesysCloudDefQueueMapping
{

    public class QueueMapping
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string nextUri { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public User user { get; set; }
        public int ringNumber { get; set; }
        public bool joined { get; set; }
        public string memberBy { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public Chat chat { get; set; }
        public string department { get; set; }
        public string email { get; set; }
        public Primarycontactinfo[] primaryContactInfo { get; set; }
        public Address[] addresses { get; set; }
        public string state { get; set; }
        public string title { get; set; }
        public string username { get; set; }
        public Manager manager { get; set; }
        public int version { get; set; }
        public bool acdAutoAnswer { get; set; }
        public string selfUri { get; set; }
        public Image[] images { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Chat
    {
        public string jabberId { get; set; }
    }

    public class Manager
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Primarycontactinfo
    {
        public string address { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
    }

    public class Address
    {
        public string address { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
    }

    public class Image
    {
        public string resolution { get; set; }
        public string imageUri { get; set; }
    }

}

namespace GenesysCloudDefQueueAuditing
{


    public class AuditJob
    {
        public string id { get; set; }
        public string state { get; set; }
        public DateTime startDate { get; set; }
        public string interval { get; set; }
        public string serviceName { get; set; }
        public object[] filters { get; set; }
    }



    public class AuditChange
    {
        public string id { get; set; }
        public int pageSize { get; set; }
        public string cursor { get; set; }
        public Entity[] entities { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string userHomeOrgId { get; set; }
        public User user { get; set; }
        public Client client { get; set; }
        public object[] remoteIp { get; set; }
        public string serviceName { get; set; }
        public DateTime eventDate { get; set; }
        public string action { get; set; }
        public Entity1 entity { get; set; }
        public string entityType { get; set; }
        public Propertychange[] propertyChanges { get; set; }
        public Context context { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Client
    {
        public string id { get; set; }
    }

    public class Entity1
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Context
    {
    }

    public class Propertychange
    {
        public string property { get; set; }
        public string[] oldValues { get; set; }
        public string[] newValues { get; set; }
    }



}

namespace GenesysCloudDefScheduleForecast
{

    public class Forecasting
    {
        public Result result { get; set; }
    }

    public class Result
    {
        public Entity[] entities { get; set; }
        public DateTime referenceStartDate { get; set; }
    }

    public class Entity
    {
        public Planninggroup planningGroup { get; set; }
        public float[] requiredPerInterval { get; set; }
        public float[] requiredWithoutShrinkagePerInterval { get; set; }
    }

    public class Planninggroup
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefDivisionDetails
{

    public class Divisions
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public bool homeDivision { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefVoiceAnalysisOverview
{


    public class VoiceAnalysisOverview
    {
        public Conversation conversation { get; set; }
        public float? sentimentScore { get; set; }
        public float sentimentTrend { get; set; }
        public string sentimentTrendClass { get; set; }
        public Participantmetrics participantMetrics { get; set; }
    }

    public class Conversation
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Participantmetrics
    {
        public float agentDurationPercentage { get; set; }
        public float customerDurationPercentage { get; set; }
        public float silenceDurationPercentage { get; set; }
        public float ivrDurationPercentage { get; set; }
        public float acdDurationPercentage { get; set; }
        public float overtalkDurationPercentage { get; set; }
        public float otherDurationPercentage { get; set; }
        public int overtalkCount { get; set; }
    }


}

namespace GenesysCloudDefVoiceAnalysisDetail
{

    public class VoiceDetail
    {
        public string organizationId { get; set; }
        public string conversationId { get; set; }
        public string communicationId { get; set; }
        public Transcript[] transcripts { get; set; }
        public Participant[] participants { get; set; }
        public string uri { get; set; }
        public long startTime { get; set; }
        public Duration duration { get; set; }
        public string mediaType { get; set; }
        public long conversationStartTime { get; set; }
    }

    public class Duration
    {
        public int milliseconds { get; set; }
    }

    public class Transcript
    {
        public string transcriptId { get; set; }
        public string language { get; set; }
        public string programId { get; set; }
        public long startTime { get; set; }
        public Phrase[] phrases { get; set; }
        public Analytics analytics { get; set; }
        public string engineId { get; set; }
        public Duration3 duration { get; set; }
    }

    public class Analytics
    {
        public Acoustic[] acoustics { get; set; }
        public Sentiment[] sentiment { get; set; }
        public Topic[] topics { get; set; }
    }

    public class Acoustic
    {
        public string eventType { get; set; }
        public long offsetMs { get; set; }
        public long startTimeMs { get; set; }
        public long durationMs { get; set; }
        public string participant { get; set; }
    }

    public class Sentiment
    {
        public string participant { get; set; }
        public string phrase { get; set; }
        public long startTimeMs { get; set; }
        public Duration1 duration { get; set; }
        public float sentiment { get; set; }
        public int phraseIndex { get; set; }
    }

    public class Duration1
    {
        public int milliseconds { get; set; }
    }

    public class Topic
    {
        public string participant { get; set; }
        public string topicId { get; set; }
        public string topicName { get; set; }
        public string topicPhrase { get; set; }
        public string transcriptPhrase { get; set; }
        public int confidence { get; set; }
        public long startTimeMs { get; set; }
        public Duration2 duration { get; set; }
    }

    public class Duration2
    {
        public int milliseconds { get; set; }
    }

    public class Duration3
    {
        public int milliseconds { get; set; }
    }

    public class Phrase
    {
        public string text { get; set; }
        public float stability { get; set; }
        public long startTimeMs { get; set; }
        public Duration4 duration { get; set; }
        public Word[] words { get; set; }
        public object[] alternatives { get; set; }
        public string participantPurpose { get; set; }
    }

    public class Duration4
    {
        public int milliseconds { get; set; }
    }

    public class Word
    {
        public string word { get; set; }
        public float confidence { get; set; }
        public long startTimeMs { get; set; }
        public Duration5 duration { get; set; }
    }

    public class Duration5
    {
        public int milliseconds { get; set; }
    }

    public class Participant
    {
        public string participantPurpose { get; set; }
        public string initialDirection { get; set; }
        public string queueId { get; set; }
        public string divisionId { get; set; }
        public string ani { get; set; }
        public string dnis { get; set; }
        public long startTimeMs { get; set; }
        public long endTimeMs { get; set; }
        public string flowId { get; set; }
        public string flowVersion { get; set; }
        public string userId { get; set; }
    }

}

namespace GenesysCloudDefVoiceAnalysisDetURL
{

    public class VoiceAnalysisURL
    {
        public string url { get; set; }
    }

}

namespace GenesysCloudDefQueueDetails
{

    public class QueueObject
    {
        public QueueEntity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string nextUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class QueueEntity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public Division division { get; set; }
        public DateTime dateModified { get; set; }
        public string modifiedBy { get; set; }
        public int memberCount { get; set; }
        public int userMemberCount { get; set; }
        public int joinedMemberCount { get; set; }
        public Mediasettings mediaSettings { get; set; }
        public object[] routingRules { get; set; }
        public Acwsettings acwSettings { get; set; }
        public string skillEvaluationMethod { get; set; }
        public Whisperprompt whisperPrompt { get; set; }
        public bool autoAnswerOnly { get; set; }
        public Defaultscripts defaultScripts { get; set; }
        public string selfUri { get; set; }
        public bool enableTranscription { get; set; }
        public bool enableManualAssignment { get; set; }
        public DateTime dateCreated { get; set; }
        public string createdBy { get; set; }
        public Outboundmessagingaddresses outboundMessagingAddresses { get; set; }
        public Outboundemailaddress outboundEmailAddress { get; set; }
        public Queueflow queueFlow { get; set; }
        public string callingPartyNumber { get; set; }
        public bool isActive { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Mediasettings
    {
        public Call call { get; set; }
        public Socialexpression socialExpression { get; set; }
        public Chat chat { get; set; }
        public Callback callback { get; set; }
        public Message message { get; set; }
        public Videocomm videoComm { get; set; }
        public Email email { get; set; }
    }

    public class Call
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel serviceLevel { get; set; }
    }

    public class Servicelevel
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Socialexpression
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel1 serviceLevel { get; set; }
    }

    public class Servicelevel1
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Chat
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel2 serviceLevel { get; set; }
    }

    public class Servicelevel2
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Callback
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel3 serviceLevel { get; set; }
    }

    public class Servicelevel3
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Message
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel4 serviceLevel { get; set; }
    }

    public class Servicelevel4
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Videocomm
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel5 serviceLevel { get; set; }
    }

    public class Servicelevel5
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Email
    {
        public int alertingTimeoutSeconds { get; set; }
        public Servicelevel6 serviceLevel { get; set; }
    }

    public class Servicelevel6
    {
        public float percentage { get; set; }
        public int durationMs { get; set; }
    }

    public class Acwsettings
    {
        public string wrapupPrompt { get; set; }
        public int timeoutMs { get; set; }
    }

    public class Whisperprompt
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Defaultscripts
    {
        public CALL1 CALL { get; set; }
        public CHAT1 CHAT { get; set; }
    }

    public class CALL1
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class CHAT1
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Outboundmessagingaddresses
    {
        public Smsaddress smsAddress { get; set; }
    }

    public class Smsaddress
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Outboundemailaddress
    {
        public Domain domain { get; set; }
        public Route route { get; set; }
    }

    public class Domain
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Route
    {
        public string id { get; set; }
        public string pattern { get; set; }
        public object[] autoBcc { get; set; }
        public string selfUri { get; set; }
    }

    public class Queueflow
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefWFMAuditing
{
    public class AuditJob
    {
        public string id { get; set; }
        public string state { get; set; }
        public DateTime startDate { get; set; }
        public string interval { get; set; }
        public string serviceName { get; set; }
        public object[] filters { get; set; }
    }

    public class WFMAudit
    {
        public string id { get; set; }
        public int pageSize { get; set; }
        public string cursor { get; set; }
        public Entity[] entities { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string userHomeOrgId { get; set; }
        public User user { get; set; }
        public Client client { get; set; }
        public object[] remoteIp { get; set; }
        public string serviceName { get; set; }
        public DateTime eventDate { get; set; }
        public string action { get; set; }
        public Entity1 entity { get; set; }
        public string entityType { get; set; }
        public Propertychange[] propertyChanges { get; set; }
        public Context context { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Client
    {
        public string id { get; set; }
    }

    public class Entity1
    {
        public string id { get; set; }
        public string name { get; set; }
    }

    public class Context
    {
        public string BusinessUnitId { get; set; }
        public string WeekDate { get; set; }
    }

    public class Propertychange
    {
        public string property { get; set; }
        public object[] oldValues { get; set; }
        public string[] newValues { get; set; }
    }

}

namespace GenesysCloudDefWFMForecast
{

    public class Rootobject
    {
        public Result result { get; set; }
    }

    public class Result
    {
        public Entity[] entities { get; set; }
        public DateTime referenceStartDate { get; set; }
    }

    public class Entity
    {
        public Planninggroup planningGroup { get; set; }
        public float[] requiredPerInterval { get; set; }
        public float[] requiredWithoutShrinkagePerInterval { get; set; }
    }

    public class Planninggroup
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}

namespace GenesysCloudDefHeadCountForecast
{

    public class HeadCountForecast
    {
        public Result result { get; set; }
    }

    public class Result
    {
        public Entity[] entities { get; set; }
        public DateTime referenceStartDate { get; set; }
    }

    public class Entity
    {
        public Planninggroup planningGroup { get; set; }
        public float[] requiredPerInterval { get; set; }
        public float[] requiredWithoutShrinkagePerInterval { get; set; }
    }

    public class Planninggroup
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

}
namespace GenesysCloudDefLearningModules
{
    public class LearningModules
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
        public string previousUri { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string version { get; set; }
        public string externalId { get; set; }
        public string source { get; set; }
        public string type { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public bool enforceContentOrder {get; set;}
        public int? completionTimeInDays {get; set;}
        public float? lengthInMinutes {get; set;}
        public bool isPublished {get; set;}
        public bool isArchived {get; set;}

    }
}
namespace GenesysCloudDefLearningModuleAssignments
{
    public class LearningAssignment
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
        public string previousUri { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public Assesment assessment { get; set; }
        public Module module { get; set; }
        public User user { get; set; }
        public string version { get; set; }
        public bool isOverdue { get; set; }
        public float? percentageScore { get; set; }
        public float? assessmentPercentageScore { get; set; }
        public bool isRule { get; set; }
        public bool isManual { get; set; }
        public bool isPassed { get; set; }
        public bool isLatest { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public bool enforceContentOrder {get; set;}
        public float? assessmentCompletionPercentage {get; set;}
        public float? completionPercentage {get; set;}
        public DateTime dateSubmitted { get; set; }
        public DateTime dateRecommendedForCompletion { get; set; }
    }
    public class Assesment
    {
        public string id { get; set; }
    }
    public class Module
    {
        public string id { get; set; }
    }
    public class User
    {
        public string id { get; set; }
    }
}
namespace GenesysCloudDefLearningAssignmentAggragateQuery
{
    public class LearningAssignmentQuery
    {
        public Result[] results { get; set; }
    }
    public class Result
    {
        public Data data { get; set; }
    }
    public class Data
    {
        public string metric { get; set; }
        public string interval { get; set; }

        public float? statscount {get; set;}
        public float? statsmax {get; set;}
        public float? statsmin {get; set;}
        public float? statssum {get; set;}
    }
}
namespace GenesysCloudDefLearningAssignmentResults
{
    public class LearningAssignmentResult
    {
        public string id {get; set;}
        public Assessment assessment { get; set; }
        public Module module { get; set;}
        public AssessmentForm assessmentForm { get; set;}
        public float? assessmentPercentageScore{get; set;}
        public float? assessmentCompletionPercentage{get; set;}
        public float? completionPercentage{get; set;}
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }

    }
    public class Assessment
    {
        public string assessmentId { get; set; }
    }
    public class Module
    {
        public string id { get; set; }
    }
    public class AssessmentForm
    {
        public string id { get; set; }
        public float? passPercent {get; set;}
    }
}

namespace GenesysCloudDefKnowledgeBase
{
    public class KnowledgeBase
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
        public string previousUri { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string coreLanguage { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public DateTime dateDocumentLastModified { get; set; }
        public string selfUri { get; set; }
        public int? faqCount {get; set;}
        public int? articleCount {get; set;}
        public bool published {get; set;}
    }
}

namespace GenesysCloudDefKnowledgeBaseCategory
{
    public class KnowledgeBaseCategoryData
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
        public string previousUri { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string externalId { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public int? documentCount {get; set;}
        public KnowledgeBase knowledgeBase{get; set;}    
        public ParentCategory parentCategory{get; set;}    
        public string selfUri { get; set; }

    }
    public class KnowledgeBase
    {
        public string id {get; set;}
    }
    public class ParentCategory
    {
        public string name {get; set;}
        public string id {get; set;}
    }
}

namespace GenesysCloudDefKnowledgeBaseDocument
{
    public class KnowledgeBaseDocument
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
        public string previousUri { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public string title { get; set; }
        public bool visible { get; set; }
        public string state { get; set; }
        public DateTime dateCreated { get; set; }
        public DateTime dateModified { get; set; }
        public DateTime dateImported { get; set; }
        public DateTime datePublished { get; set; }
        public string selfUri { get; set; }
        public int? lastPublishedVersionNumber {get; set;}
        public string documentVersion {get; set;}
        public string externalId {get; set;}      
        //public bool readonly {get; set;}
        public KnowledgeBase knowledgeBase {get; set;}
        public Category category {get; set;}
    }
    public class KnowledgeBase
    {
        public string id {get; set;}
    }
    public class Category
    {
        public string id {get; set;}
    }
}

namespace GenesysCloudDefKnowledgeBaseDocumentVersion
{
    public class KnowledgeBaseDocumentVersion
    {
        public Entity[] entities { get; set; }
        public string nextUri { get; set; }
        public string selfUri { get; set; }
        public string previousUri { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public DateTime datePublished { get; set; }
        public DateTime dateExpires { get; set; }
        public int? versionNumber {get; set;}
        public Document document{get; set;}    
        public string selfUri { get; set; }

    }
    public class Document
    {
        public string id {get; set;}
    }
}

namespace GenesysCloudDefFlowOutcomes
{
    public class FlowOutcome
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string lastUri { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string nextUri { get; set; }
        public string previousUri { get; set; }
        public int pageCount { get; set; }
    }
    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public string description { get; set; }
        public Operation currentOperation { get; set; }
        public string selfUri { get; set; }
    }
    public class Operation
    {
        public string id { get; set; }
        public bool complete { get; set; }
        public User user { get; set; }
        public Client client { get; set; }
        public string errorMessage { get; set; }
        public string errorCode { get; set; }
        public string actionName { get; set; }
        public string actionStatus { get; set; }
    }
    public class User
    {
        public string id { get; set; }
    }
    public class Client
    {
        public string id { get; set; }
    }
    public class Division
    {
        public string id { get; set; }
    }
}

// spell-checker: ignore: systemtoorganizationmappings mediauris lookupidtosecondarypresenceid submittedby reviewedby