using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using StandardUtils;
using System.Text;
using Newtonsoft.Json;
using System.Linq;
using System.Net;
using DBUtils;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    internal class ChatObject
    {
        public string id { get; set; }
        public string conversationId { get; set; }
        public string media { get; set; }
        public Transcript[] transcript { get; set; }
        public string fileState { get; set; }
        public int estimatedTranscodeTimeMs { get; set; }
        public int actualTranscodeTimeMs { get; set; }
        public int maxAllowedRestorationsForOrg { get; set; }
        public int remainingRestorationsAllowedForOrg { get; set; }
        public string selfUri { get; set; }
    }

    internal class Transcript
    {
        public string body { get; set; }
        public string id { get; set; }
        public string to { get; set; }
        public string from { get; set; }
        public DateTime utc { get; set; }
        public string chat { get; set; }
        public string type { get; set; }
        public string bodyType { get; set; }
    }

    public class MessageData
    {

        public string? CustomerKeyID { get; set; }
        public string? GCApiKey { get; set; }
        public DateTime DetailMessageLastUpdate { get; set; }
        public DataSet? GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des? UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string? TimeZoneConfig { get; set; }

        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public MessageData(ILogger logger)
        {
            _logger = logger;

            GCUtilities.Initialize();

            Console.WriteLine("Initialization of GC Message Data V1.20.26.11.01");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            DBUtil.Initialize();
        }

        public DataTable GetMessageDataFromGC(string ConversationId)
        {
            Console.WriteLine("Retrieving Message Data from {0} ", ConversationId);
            DataTable MessageData = CreateRawMessageTable();
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            try
            {
                StringBuilder JsonString = new StringBuilder(JsonActions.JsonReturnString(URI + "/api/v2/conversations/" + ConversationId + "/recordings?formatId=mp3", GCApiKey));

                JsonString.Length = JsonString.Length - 1;
                JsonString.Remove(0, 1);

                ChatObject JSON = new ChatObject();

                JSON = JsonConvert.DeserializeObject<ChatObject>(JsonString.ToString(),
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                if (JSON.transcript != null)
                {
                    foreach (Transcript ConversationLine in JSON.transcript)
                    {
                        //Console.WriteLine("Found Conversation Line");
                        DataRow MessageLine = MessageData.NewRow();
                        MessageLine["conversationid"] = ConversationId;
                        MessageLine["to"] = ConversationLine.to;
                        MessageLine["from"] = ConversationLine.from;
                        MessageLine["utc"] = ConversationLine.utc;
                        MessageLine["keyid"] = ConversationId + ConversationLine.utc.ToString();
                        MessageLine["conversationid"] = ConversationId;
                        MessageData.Rows.Add(MessageLine);
                    }

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error In GenesysCloud.Message.GetMessageDataFromGC :{0} Conversation ID: {1}", ex.ToString(), ConversationId);
            }

            return MessageData;
        }

        public DataTable CalculateMessageData(
            string StartDate,
            string EndDate,
            DataTable Users,
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            bool ForSQL = false)
        {

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DateTime TempDetailMessageLastUpdate = DetailMessageLastUpdate;
            DetailData Segments = new DetailData(_logger, renameParticipantAttributeNames);
            Segments.TimeZoneConfig = TimeZoneConfig;

            DataTable TBLSegments = Segments.GetDetailInteractionDataFromGCQuery(StartDate, EndDate).Tables[0];
            DataTable CalculatedMessageData = CreateCalculatedMessageTable();

            Console.WriteLine("We have {0} Rows in the Segments", TBLSegments.Rows.Count);

            string LastConversationID = String.Empty;

            foreach (DataRow SegmentRow in TBLSegments.Select("MediaType='message'"))
            {
                if (SegmentRow["conversationid"].ToString() != LastConversationID)
                {
                    string LastPerson = string.Empty;
                    string EmpOrCust = string.Empty;
                    string EmpName = string.Empty;
                    string EmpGroup = string.Empty;
                    string EmpUserId = string.Empty;
                    string MessageInitiatedBy = string.Empty;
                    Boolean FirstMessage = true;

                    int AgentCount = 0;
                    int CustCount = 0;

                    double AgentTime = 0;
                    double AgentMinTime = 10000000;
                    double AgentMaxTime = 0;
                    double CustTime = 0;
                    double CustMinTime = 10000000;
                    double CustMaxTime = 0;
                    double AgentResponseTime = 0;
                    double CustResponseTime = 0;

                    DateTime LastMessageDate = DateTime.Now;
                    DataTable MessageData = GetMessageDataFromGC(SegmentRow["conversationid"].ToString());

                    foreach (DataRow MessageLine in MessageData.Rows)
                    {
                        //Console.WriteLine("Message From: {0} Time: {1} ", (String)MessageLine["from"], (String)MessageLine["utc"]);

                        if (LastPerson != MessageLine["from"].ToString())
                        {
                            //Check is this Employee or Customer
                            DataRow GCUser = Users.Select("jabberid='" + MessageLine["from"].ToString().Replace("'", "''") + "'").FirstOrDefault();

                            if (GCUser != null)
                            {
                                EmpOrCust = "EMP";
                                EmpName = (string)GCUser["Name"];
                                EmpUserId = (string)GCUser["id"];

                                if (ForSQL == false)
                                    EmpGroup = (string)GCUser["Group"];
                            }
                            else
                                EmpOrCust = "CUST";

                            switch (EmpOrCust)
                            {
                                case "EMP":
                                    if (FirstMessage != true)
                                        AgentResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - LastMessageDate).TotalSeconds);
                                    else
                                        AgentResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - (DateTime)SegmentRow["conversationstartdate"]).TotalSeconds);

                                    AgentCount++;
                                    AgentTime = AgentTime + AgentResponseTime;
                                    if (AgentResponseTime > AgentMaxTime)
                                        AgentMaxTime = AgentResponseTime;
                                    if (AgentResponseTime < AgentMinTime)
                                        AgentMinTime = AgentResponseTime;

                                    break;
                                case "CUST":
                                    if (FirstMessage != true)
                                        CustResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - LastMessageDate).TotalSeconds);
                                    else
                                        CustResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - (DateTime)SegmentRow["conversationstartdate"]).TotalSeconds);

                                    CustCount++;
                                    CustTime = CustTime + CustResponseTime;

                                    if (CustResponseTime > CustMaxTime)
                                        CustMaxTime = CustResponseTime;
                                    if (CustResponseTime < CustMinTime)
                                        CustMinTime = CustResponseTime;
                                    break;

                            }
                            LastPerson = (String)MessageLine["from"];
                            LastMessageDate = DateTime.Parse(MessageLine["utc"].ToString());

                            if (FirstMessage == true)
                            {
                                MessageInitiatedBy = EmpOrCust;
                                FirstMessage = false;
                            }

                        }
                    }

                    if (AgentMinTime >= AgentMaxTime)
                        AgentMinTime = AgentMaxTime;

                    if (CustMinTime >= CustMaxTime)
                        CustMinTime = CustMaxTime;

                    LastConversationID = SegmentRow["conversationid"].ToString();

                    if (AgentCount + CustCount > 0)
                    {
                        DataRow MessageRow = CalculatedMessageData.NewRow();

                        MessageRow["keyid"] = (string)SegmentRow["conversationid"];
                        MessageRow["ConversationId"] = (string)SegmentRow["conversationid"];
                        MessageRow["conversationstart"] = (DateTime)SegmentRow["conversationstartdate"];
                        MessageRow["conversationstartltc"] = TimeZoneInfo.ConvertTimeFromUtc((DateTime)SegmentRow["conversationstartdate"], AppTimeZone); ;
                        MessageRow["Name"] = EmpName;
                        MessageRow["userid"] = EmpUserId;
                        if (ForSQL == false)
                            MessageRow["Group"] = EmpGroup;
                        MessageRow["LinkedDate"] = (DateTime)SegmentRow["conversationstartdate"];
                        MessageRow["messageinitiatedby"] = MessageInitiatedBy;
                        MessageRow["agentmessagecount"] = AgentCount;
                        MessageRow["agentmessagetotal"] = AgentTime;
                        MessageRow["agentmessagemax"] = AgentMaxTime;
                        MessageRow["agentmessagemin"] = AgentMinTime;
                        MessageRow["custmessagecount"] = CustCount;
                        MessageRow["custmessagetotal"] = CustTime;
                        MessageRow["custmessagemax"] = CustMaxTime;
                        MessageRow["custmessagemin"] = CustMinTime;


                        DateTime MaxDateTest = DateTime.Parse(SegmentRow["conversationstartdate"].ToString()).ToUniversalTime();
                        if (MaxDateTest > DetailMessageLastUpdate)
                        {
                            DetailMessageLastUpdate = MaxDateTest;
                            Console.Write("@");
                        }

                        CalculatedMessageData.Rows.Add(MessageRow);
                    }
                }
            }

            if (ForSQL == true)
            {

                DataTable CalculatedMessageDataSQL = CreateCalculatedMessageTableSQL();


                foreach (DataRow OldData in CalculatedMessageData.Rows)
                {
                    DataRow NewSQLRow = CalculatedMessageDataSQL.NewRow();


                    NewSQLRow["keyid"] = UCAUtils.GetStableHashCode(OldData["keyid"].ToString());
                    NewSQLRow["conversationid"] = OldData["ConversationId"];
                    NewSQLRow["conversationstart"] = OldData["ConversationStart"];
                    NewSQLRow["conversationstartltc"] = OldData["ConversationStartltc"];
                    NewSQLRow["userid"] = OldData["userid"];
                    NewSQLRow["messageinitiatedby"] = OldData["messageinitiatedby"];
                    NewSQLRow["agentmessagecount"] = OldData["agentmessagecount"];
                    NewSQLRow["agentmessagetotal"] = OldData["agentmessagetotal"];
                    NewSQLRow["agentmessagemax"] = OldData["agentmessagemax"];
                    NewSQLRow["agentmessagemin"] = OldData["agentmessagemin"];
                    NewSQLRow["custmessagecount"] = OldData["custmessagecount"];
                    NewSQLRow["custmessagetotal"] = OldData["custmessagetotal"];
                    NewSQLRow["custmessagemax"] = OldData["custmessagemax"];
                    NewSQLRow["custmessagemin"] = OldData["custmessagemin"];
                    CalculatedMessageDataSQL.Rows.Add(NewSQLRow);
                }
                return CalculatedMessageDataSQL;
            }
            else
            {
                return CalculatedMessageData;

            }
        }

        public DataTable CalculateMessageDataForChatTable(
            string StartDate,
            string EndDate,
            DataTable Users,
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            bool ForSQL = false)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DateTime TempDetailMessageLastUpdate = DetailMessageLastUpdate;
            DetailData Segments = new DetailData(_logger, renameParticipantAttributeNames);
            Segments.TimeZoneConfig = TimeZoneConfig;

            DataTable TBLSegments = Segments.GetDetailInteractionDataFromGCQuery(StartDate, EndDate).Tables[0];
            DataTable CalculatedMessageData = CreateCalculatedChatTableForMessage();

            Console.WriteLine("We have {0} Rows in the Segments", TBLSegments.Rows.Count);

            string LastConversationID = String.Empty;

            foreach (DataRow SegmentRow in TBLSegments.Select("MediaType='message'"))
            {
                if (SegmentRow["conversationid"].ToString() != LastConversationID)
                {
                    string LastPerson = string.Empty;
                    string EmpOrCust = string.Empty;
                    string EmpName = string.Empty;
                    string EmpGroup = string.Empty;
                    string EmpUserId = string.Empty;
                    string MessageInitiatedBy = string.Empty;
                    Boolean FirstMessage = true;

                    int AgentCount = 0;
                    int CustCount = 0;

                    double AgentTime = 0;
                    double AgentMinTime = 10000000;
                    double AgentMaxTime = 0;
                    double CustTime = 0;
                    double CustMinTime = 10000000;
                    double CustMaxTime = 0;
                    double AgentResponseTime = 0;
                    double CustResponseTime = 0;

                    DateTime LastMessageDate = DateTime.Now;
                    DataTable MessageData = GetMessageDataFromGC(SegmentRow["conversationid"].ToString());

                    foreach (DataRow MessageLine in MessageData.Rows)
                    {
                        if (LastPerson != MessageLine["from"].ToString())
                        {
                            //Check is this Employee or Customer
                            DataRow GCUser = Users.Select("jabberid='" + MessageLine["from"].ToString().Replace("'", "''") + "'").FirstOrDefault();

                            if (GCUser != null)
                            {
                                EmpOrCust = "EMP";
                                EmpName = (string)GCUser["Name"];
                                EmpUserId = (string)GCUser["id"];

                                if (ForSQL == false)
                                    EmpGroup = (string)GCUser["Group"];
                            }
                            else
                                EmpOrCust = "CUST";

                            switch (EmpOrCust)
                            {
                                case "EMP":
                                    if (FirstMessage != true)
                                        AgentResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - LastMessageDate).TotalSeconds);
                                    else
                                        AgentResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - (DateTime)SegmentRow["conversationstartdate"]).TotalSeconds);

                                    AgentCount++;
                                    AgentTime = AgentTime + AgentResponseTime;
                                    if (AgentResponseTime > AgentMaxTime)
                                        AgentMaxTime = AgentResponseTime;
                                    if (AgentResponseTime < AgentMinTime)
                                        AgentMinTime = AgentResponseTime;

                                    break;
                                case "CUST":
                                    if (FirstMessage != true)
                                        CustResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - LastMessageDate).TotalSeconds);
                                    else
                                        CustResponseTime = ((DateTime.Parse(MessageLine["utc"].ToString()) - (DateTime)SegmentRow["conversationstartdate"]).TotalSeconds);

                                    CustCount++;
                                    CustTime = CustTime + CustResponseTime;

                                    if (CustResponseTime > CustMaxTime)
                                        CustMaxTime = CustResponseTime;
                                    if (CustResponseTime < CustMinTime)
                                        CustMinTime = CustResponseTime;
                                    break;

                            }
                            LastPerson = (String)MessageLine["from"];
                            LastMessageDate = DateTime.Parse(MessageLine["utc"].ToString());

                            if (FirstMessage == true)
                            {
                                MessageInitiatedBy = EmpOrCust;
                                FirstMessage = false;
                            }

                        }
                    }

                    if (AgentMinTime >= AgentMaxTime)
                        AgentMinTime = AgentMaxTime;

                    if (CustMinTime >= CustMaxTime)
                        CustMinTime = CustMaxTime;

                    LastConversationID = SegmentRow["conversationid"].ToString();

                    if (AgentCount + CustCount > 0)
                    {
                        DataRow MessageRow = CalculatedMessageData.NewRow();

                        MessageRow["keyid"] = UCAUtils.GetStableHashCode(SegmentRow["conversationid"].ToString());
                        MessageRow["conversationid"] = (string)SegmentRow["conversationid"];
                        MessageRow["conversationstart"] = (DateTime)SegmentRow["conversationstartdate"];
                        MessageRow["conversationstartltc"] = TimeZoneInfo.ConvertTimeFromUtc((DateTime)SegmentRow["conversationstartdate"], AppTimeZone);
                        MessageRow["userid"] = EmpUserId;
                        MessageRow["chatinitiatedby"] = MessageInitiatedBy; // Using chat column names for unified storage
                        MessageRow["agentchatcount"] = AgentCount;
                        MessageRow["agentchattotal"] = AgentTime;
                        MessageRow["agentchatmax"] = AgentMaxTime;
                        MessageRow["agentchatmin"] = AgentMinTime;
                        MessageRow["custchatcount"] = CustCount;
                        MessageRow["custchattotal"] = CustTime;
                        MessageRow["custchatmax"] = CustMaxTime;
                        MessageRow["custchatmin"] = CustMinTime;
                        MessageRow["mediatype"] = "message"; // Set mediatype to distinguish from chat data

                        DateTime MaxDateTest = DateTime.Parse(SegmentRow["conversationstartdate"].ToString()).ToUniversalTime();
                        if (MaxDateTest > DetailMessageLastUpdate)
                        {
                            DetailMessageLastUpdate = MaxDateTest;
                            Console.Write("@");
                        }

                        CalculatedMessageData.Rows.Add(MessageRow);
                    }
                }
            }

            return CalculatedMessageData;
        }

        private DataTable CreateCalculatedChatTableForMessage()
        {
            DataTable DTTemp = new DataTable();
            String ChatSQL = String.Empty;

            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    ChatSQL = "SELECT TOP (0) * FROM chatData";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    ChatSQL = "SELECT  * FROM chatData LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            DTTemp = DBUtil.GetSQLTableData(ChatSQL, "chatData").Clone();
            DTTemp.TableName = "chatData";
            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            return DTTemp;
        }

        private DataTable CreateCalculatedMessageTable()
        {
            DataTable MessageTable = new DataTable();
            MessageTable.TableName = "MessageCalculated";
            MessageTable.Columns.Add("keyid", typeof(String));
            MessageTable.Columns.Add("ConversationId", typeof(String));
            MessageTable.Columns.Add("Name", typeof(String));
            MessageTable.Columns.Add("userid", typeof(String));
            MessageTable.Columns.Add("Group", typeof(String));
            MessageTable.Columns.Add("ConversationStart", typeof(DateTime));
            MessageTable.Columns.Add("ConversationStartltc", typeof(DateTime));
            MessageTable.Columns.Add("LinkedDate", typeof(DateTime));
            MessageTable.Columns.Add("messageinitiatedby", typeof(String));
            MessageTable.Columns.Add("agentmessagecount", typeof(double));
            MessageTable.Columns.Add("agentmessagetotal", typeof(double));
            MessageTable.Columns.Add("agentmessagemax", typeof(double));
            MessageTable.Columns.Add("agentmessagemin", typeof(double));
            MessageTable.Columns.Add("custmessagecount", typeof(double));
            MessageTable.Columns.Add("custmessagetotal", typeof(double));
            MessageTable.Columns.Add("custmessagemax", typeof(double));
            MessageTable.Columns.Add("custmessagemin", typeof(double));
            MessageTable.Columns.Add("updated", typeof(DateTime));
            DataColumn[] key = new DataColumn[1];
            key[0] = MessageTable.Columns[0];
            MessageTable.PrimaryKey = key;
            return MessageTable;
        }

        private DataTable CreateCalculatedMessageTableSQL()
        {

            DataTable DTTemp = new DataTable();

            String MessageSQL = String.Empty;


            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    MessageSQL = "SELECT TOP (0) * FROM messageData";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    MessageSQL = "SELECT  * FROM messageData LIMIT 0";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            DTTemp = DBUtil.GetSQLTableData(MessageSQL, "messageData").Clone();
            DTTemp.TableName = "messageData";
            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            return DTTemp;

        }

        private DataTable CreateRawMessageTable()
        {
            DataTable MessageTable = new DataTable();

            MessageTable.Columns.Add("keyid", typeof(String));
            MessageTable.Columns.Add("conversationId", typeof(String));
            MessageTable.Columns.Add("to", typeof(String));
            MessageTable.Columns.Add("from", typeof(String));
            MessageTable.Columns.Add("utc", typeof(String));
            return MessageTable;

        }

    }

}
// spell-checker: ignore: jabberid, messageinitiatedby
