﻿#nullable enable
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;           // Added for HttpWebRequest, HttpWebResponse, etc.
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StandardUtils;
using OauthUsage = GenesysCloudDefOauthUsage;
using Subs = GenesysCloudDefSubscriptions;
using SubsOver = GenesysCloudDefSubscriptionOverView;
using SubUsers = GenesysCloudDefSubUsersUsage;
using Teams = GenesysCloudDefTeamDetailed;
using TeamsMembers = GenesysCloudDefTeamMembers;

namespace GenesysCloudUtils
{
    public class adminData
    {
        #region Fields and Properties

        public string CustomerKeyID { get; set; } = string.Empty;
        public string GCApiKey { get; set; } = string.Empty;
        public DataSet GCControlData { get; set; } = new DataSet();
        public string TimeZoneConfig { get; set; } = string.Empty;
        public string OAuthUser { get; set; } = string.Empty;

        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption = null!;
        private readonly GCUtils GCUtilities;
        private readonly JsonUtils JsonActions;

        private DataTable DownloadTable { get; set; } = new DataTable();
        private int TotalResponses { get; set; }
        private bool CanContinue { get; set; }

        private readonly ILogger? _logger;

        #endregion

        #region Constructors and Initialization

        public adminData() : this(null)
        {
        }

        public adminData(ILogger? logger)
        {
            _logger = logger;
            GCUtilities = new GCUtils(logger);
            JsonActions = new JsonUtils(logger);
        }

        public void Initialize()
        {
            _logger?.LogInformation("Initializing GenesysCloud adminData");
            GCUtilities.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            _logger?.LogDebug("Obtaining Genesys Cloud API key");
            GCApiKey = GCUtilities.GCApiKey;
            _logger?.LogInformation("Initialization complete.");
        }

        #endregion

        #region System Call Usage

        public DataTable GetSystemCallUsage(int dayOffset)
        {
            _logger?.LogInformation("Starting GetSystemCallUsage for dayOffset: {DayOffset}", dayOffset);
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable systemCallUsageData = dbUtil.CreateInMemTable("systemcallusagedata");

            string fromDate = DateTime.Now.AddDays(-dayOffset).ToString("yyyy-MM-01T00:00:00", CultureInfo.InvariantCulture);
            string toDate = DateTime.Now.AddDays(-dayOffset).ToString("yyyy-MM-ddT23:59:59", CultureInfo.InvariantCulture);

            string query = $@"
                SELECT DISTINCT
                    conversationid,
                    conversationstartdateltc,
                    conversationenddateltc,
                    mediatype,
                    originaldirection,
                    purpose,
                    segmenttype,
                    segmentstartdateltc,
                    segmentenddateltc
                FROM detailedinteractiondata
                WHERE ((segmentstartdateltc < '{toDate}' AND segmentenddateltc IS NULL)
                    OR (segmentstartdateltc BETWEEN '{fromDate}' AND '{toDate}')
                    OR (segmentenddateltc BETWEEN '{fromDate}' AND '{toDate}'))
                    AND purpose NOT IN ('customer','external')
                    AND segmenttype NOT IN ('scheduled')";
            DataTable workingTable = dbUtil.GetSQLTableData(query, "detailedinteractionData");

            for (int minuteCounter = 1; minuteCounter < 1440; minuteCounter++)
            {
                string checkDate = DateTime.Parse(fromDate, CultureInfo.InvariantCulture)
                                    .AddMinutes(minuteCounter)
                                    .ToString("yyyy-MM-01THH:mm:00", CultureInfo.InvariantCulture);
                _logger?.LogDebug("Checking usage for timestamp: {CheckDate}", checkDate);

                string selectString = $"(segmentstartdateltc < '{checkDate}' and segmentenddateltc is null) " +
                                      $"or ('{checkDate}' >= segmentstartdateltc and '{checkDate}' <= segmentenddateltc)";

                DataRow[] drWorkingSet = workingTable.Select(selectString);
                string lastConversationId = string.Empty;

                foreach (DataRow segment in drWorkingSet)
                {
                    string conversationId = segment["conversationid"]?.ToString() ?? "";
                    if (conversationId != lastConversationId)
                    {
                        lastConversationId = conversationId;
                        DataRow systemCallLine = systemCallUsageData.Select(
                            $"rowdate = '{checkDate}' and mediatype = '{segment["mediatype"]}' and originaldirection = '{segment["originaldirection"]}' and purpose = '{segment["purpose"]}' and segmenttype = '{segment["segmenttype"]}'")
                            .FirstOrDefault() ?? systemCallUsageData.NewRow();

                        if (systemCallLine.RowState == DataRowState.Detached)
                        {
                            systemCallLine["keyid"] = $"{checkDate}|{segment["mediatype"]}|{segment["originaldirection"]}|{segment["purpose"]}|{segment["segmenttype"]}";
                            systemCallLine["rowdate"] = checkDate;
                            systemCallLine["mediatype"] = segment["mediatype"];
                            systemCallLine["originaldirection"] = segment["originaldirection"];
                            systemCallLine["purpose"] = segment["purpose"];
                            systemCallLine["segmenttype"] = segment["segmenttype"];
                            systemCallLine["liveinteractioncount"] = 0;
                            systemCallUsageData.Rows.Add(systemCallLine);
                        }
                        systemCallLine["liveinteractioncount"] = Convert.ToInt32(systemCallLine["liveinteractioncount"]) + 1;
                    }
                }
            }
            _logger?.LogInformation("GetSystemCallUsage completed. Total records: {Count}", systemCallUsageData.Rows.Count);
            return systemCallUsageData;
        }

        #endregion

        #region OAuth Usage

        public DataTable GetOauthUsage(int monthOffset)
        {
            _logger?.LogInformation("Starting GetOauthUsage for monthOffset: {MonthOffset}", monthOffset);
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable oauthUsageData = dbUtil.CreateInMemTable("oauthusageData");

            string fromDate = DateTime.Now.AddMonths(-monthOffset).ToString("yyyy-MM-01T00:00:00", CultureInfo.InvariantCulture);
            string toDate = DateTime.Parse(fromDate, CultureInfo.InvariantCulture)
                                .AddMonths(1)
                                .AddDays(-1)
                                .ToString("yyyy-MM-ddT23:59:59", CultureInfo.InvariantCulture);

            string jsonBody = $"{{\"interval\": \"{fromDate}/{toDate}\",\"granularity\": \"Day\",\"groupBy\": [\"OAuthClientId\"]}}";
            string urlExtra = "/api/v2/usage/query/";
            string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            _logger?.LogDebug("Requesting OAuth usage report with interval: {Interval}", $"{fromDate}/{toDate}");

            HttpApiResponse apiResponse;
            try
            {
                apiResponse = JsonActions.JsonReturnHttpResponse(uri + urlExtra, GCApiKey, jsonBody);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "API Call Error while requesting OAuth usage report: {Message}", ex.Message);
                Console.WriteLine("Error requesting OAuth usage report: {0}", ex.Message);
                return oauthUsageData;
            }

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(apiResponse.Content))
            {
                _logger?.LogWarning("Empty response received for OAuth usage report");
                Console.WriteLine("Warning: Empty response for OAuth usage report");
                return oauthUsageData;
            }

            // Handle different HTTP status codes appropriately
            if (!apiResponse.IsSuccess)
            {
                _logger?.LogError("API Error while requesting OAuth usage report: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                // Check for specific error types
                if (apiResponse.StatusCode == 403)
                {
                    _logger?.LogWarning("Permission denied (HTTP 403) when requesting OAuth usage report");
                    Console.WriteLine("Permission denied when requesting OAuth usage report");
                }
                else
                {
                    Console.WriteLine("API Error: HTTP {0} when requesting OAuth usage report", apiResponse.StatusCode);
                }
                return oauthUsageData;
            }

            OauthUsage.OauthUsageReport oauthRepData;
            try
            {
                oauthRepData = JsonConvert.DeserializeObject<OauthUsage.OauthUsageReport>(apiResponse.Content,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "JSON deserialization error for OAuth usage report: {Message}. Response content: {Content}", ex.Message, apiResponse.Content);
                Console.WriteLine("JSON error for OAuth usage report: {0}", ex.Message);
                return oauthUsageData;
            }

            if (apiResponse.Content.Length > 40 && !string.IsNullOrWhiteSpace(oauthRepData?.resultsUri))
            {
                _logger?.LogInformation("OAuth usage report started; pausing for report completion.");
                DateTime? firstRetryTime = null;
                int attempts = 0;
                const int maxAttempts = 7;
                bool successful = false;

                while (!successful && attempts < maxAttempts)
                {
                    attempts++;
                    _logger?.LogInformation("({Attempt}/{MaxAttempts}) Polling OAuth usage report: {JobId}", attempts, maxAttempts, oauthRepData.resultsUri);

                    HttpApiResponse resultsResponse;
                    try
                    {
                        resultsResponse = JsonActions.JsonReturnHttpResponseGet(uri + oauthRepData.resultsUri, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "API Call Error while polling OAuth usage report: {Message}", ex.Message);
                        Console.WriteLine("Error polling OAuth usage report: {0}", ex.Message);
                        break;
                    }

                    // Validate response using proper HTTP status code detection
                    if (string.IsNullOrWhiteSpace(resultsResponse.Content))
                    {
                        _logger?.LogWarning("Empty response received while polling OAuth usage report");
                        Console.WriteLine("Warning: Empty response while polling OAuth usage report");
                        break;
                    }

                    // Handle different HTTP status codes appropriately
                    if (!resultsResponse.IsSuccess)
                    {
                        _logger?.LogError("API Error while polling OAuth usage report: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                            resultsResponse.StatusCode, resultsResponse.StatusDescription, resultsResponse.Content);

                        // Check for specific error types
                        if (resultsResponse.StatusCode == 403)
                        {
                            _logger?.LogWarning("Permission denied (HTTP 403) while polling OAuth usage report");
                            Console.WriteLine("Permission denied while polling OAuth usage report");
                        }
                        else
                        {
                            Console.WriteLine("API Error: HTTP {0} while polling OAuth usage report", resultsResponse.StatusCode);
                        }
                        break;
                    }

                    OauthUsage.OauthUsageResult oauthResults;
                    try
                    {
                        oauthResults = JsonConvert.DeserializeObject<OauthUsage.OauthUsageResult>(resultsResponse.Content,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "JSON deserialization error while polling OAuth usage report: {Message}. Response content: {Content}", ex.Message, resultsResponse.Content);
                        Console.WriteLine("JSON error while polling OAuth usage report: {0}", ex.Message);
                        break;
                    }

                    if (!string.IsNullOrWhiteSpace(resultsResponse.Content) && resultsResponse.Content.Length > 30 && oauthResults?.queryStatus == "Complete")
                    {
                        successful = true;
                        _logger?.LogInformation("({Attempt}/{MaxAttempts}) OAuth usage report complete.", attempts, maxAttempts);
                        foreach (OauthUsage.Result oauthUsageDay in oauthResults.results)
                        {
                            try
                            {
                                DataRow oauthDay = oauthUsageData.NewRow();
                                oauthDay["clientid"] = oauthUsageDay.clientId;
                                oauthDay["status200"] = oauthUsageDay.status200;
                                oauthDay["status300"] = oauthUsageDay.status300;
                                oauthDay["status400"] = oauthUsageDay.status400;
                                oauthDay["status500"] = oauthUsageDay.status500;
                                oauthDay["status429"] = oauthUsageDay.status429;
                                oauthDay["organizationId"] = oauthUsageDay.organizationId;
                                oauthDay["userId"] = oauthUsageDay.userId;
                                oauthDay["rowdate"] = oauthUsageDay.date;
                                oauthDay["clientname"] = oauthUsageDay.clientName;
                                oauthDay["keyid"] = $"{oauthUsageDay.clientId}|{oauthUsageDay.date}";
                                oauthUsageData.Rows.Add(oauthDay);
                            }
                            catch (ConstraintException)
                            {
                                _logger?.LogDebug("Duplicate OAuth usage record skipped.");
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogWarning(ex, "Error processing OAuth usage record: {Error}", ex.ToString());
                            }
                        }
                    }
                    else
                    {
                        if (firstRetryTime == null)
                        {
                            firstRetryTime = DateTime.UtcNow;
                        }
                        TimeSpan elapsed = DateTime.UtcNow - firstRetryTime.Value;
                        int delay;
                        if (elapsed < TimeSpan.FromMinutes(5))
                        {
                            delay = (attempts == 1) ? 1000 : 3000;
                        }
                        else if (elapsed < TimeSpan.FromMinutes(10))
                        {
                            delay = 9000;
                        }
                        else
                        {
                            delay = 27000;
                        }
                        _logger?.LogInformation("({Attempt}/{MaxAttempts}) OAuth usage report not complete; elapsed time: {Elapsed}. Waiting {Delay} ms before next attempt.", attempts, maxAttempts, elapsed, delay);
                        Thread.Sleep(delay);
                    }
                }
                if (!successful)
                {
                    _logger?.LogError("Exceeded maximum polling attempts for OAuth usage report.");
                }
            }
            else
            {
                _logger?.LogWarning("OAuth usage report request did not return a valid resultsUri.");
            }
            _logger?.LogInformation("GetOauthUsage completed. Total records: {Count}", oauthUsageData.Rows.Count);
            return oauthUsageData;
        }

        #endregion

        #region Subscription User Usage

        public DataTable GetSubUserUsage(DateTime dateToSyncFrom)
        {
            _logger?.LogInformation("Starting GetSubUserUsage for date: {DateToSyncFrom}", dateToSyncFrom);
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable subscriptionData = dbUtil.CreateInMemTable("subuserusagedata");

            string fromDate = dateToSyncFrom.ToString("yyyy-MM-01 00:00:00.000");
            string toDate = DateTime.Parse(fromDate).AddMonths(1).AddDays(-1)
                                .ToString("yyyy-MM-dd 23:59:59.999");

            double fromDateUnix = UCAUtils.ConvertToUnixTimestamp(TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(fromDate), appTimeZone));
            double toDateUnix = UCAUtils.ConvertToUnixTimestamp(TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(toDate), appTimeZone));

            string urlExtra = $"/api/v2/billing/reports/hourlyLicenseUsageData/{fromDateUnix}000_{toDateUnix}990/csv";
            string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            _logger?.LogInformation("Requesting Subscription Usage CSV for period: {FromDate} to {ToDate}", fromDate, toDate);
            string jsonString = string.Empty;

            HttpApiResponse csvResponse;
            try
            {
                csvResponse = JsonActions.JsonReturnHttpResponseGet(uri + urlExtra, GCApiKey);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "API Call Error while requesting subscription usage CSV: {Message}", ex.Message);
                Console.WriteLine("Error requesting subscription usage CSV: {0}", ex.Message);
                return subscriptionData;
            }

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(csvResponse.Content))
            {
                _logger?.LogWarning("Empty response received for subscription usage CSV");
                Console.WriteLine("Warning: Empty response for subscription usage CSV");
                return subscriptionData;
            }

            // Handle different HTTP status codes appropriately
            if (!csvResponse.IsSuccess)
            {
                _logger?.LogError("API Error while requesting subscription usage CSV: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    csvResponse.StatusCode, csvResponse.StatusDescription, csvResponse.Content);

                // Check for specific error types
                if (csvResponse.StatusCode == 403)
                {
                    // Check if the response contains permanent permission issue using centralized error handling
                    if (GenesysErrorHandler.IsErrorResponse(csvResponse.Content))
                    {
                        var errorResult = GenesysErrorHandler.CreateErrorResult(csvResponse.Content);
                        if (csvResponse.Content.Contains("\"isPermanent\":true"))
                        {
                            _logger?.LogError("Permanent permission issue detected when requesting subscription usage CSV. Error: {ErrorMessage}", errorResult.ErrorMessage);
                            throw new UnauthorizedAccessException($"Permanent permission issue detected when requesting subscription usage CSV: {errorResult.ErrorMessage}");
                        }
                    }
                    else if (csvResponse.Content.Contains("\"isPermanent\":true"))
                    {
                        _logger?.LogError("Permanent permission issue detected when requesting subscription usage CSV. Unable to proceed.");
                        throw new UnauthorizedAccessException("Permanent permission issue detected when requesting subscription usage CSV");
                    }
                    else
                    {
                        _logger?.LogWarning("Temporary permission issue detected when requesting subscription usage CSV. Will continue with empty dataset.");
                        return subscriptionData; // Return empty dataset
                    }
                }
                else
                {
                    Console.WriteLine("API Error: HTTP {0} when requesting subscription usage CSV", csvResponse.StatusCode);
                    return subscriptionData;
                }
            }

            jsonString = csvResponse.Content;

            SubUsers.UserUsageTask subUserJob = JsonConvert.DeserializeObject<SubUsers.UserUsageTask>(jsonString,
                new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            if (jsonString.Length > 0 && jsonString != "{}")
            {
                SubUsers.UserUsageJob userJob = new SubUsers.UserUsageJob();
                string downloadURL = "";
                bool jobCompleted = false;

                DateTime? firstRetryTime = null;
                int attempt = 0;
                const int maxAttempts = 10;

                while (!jobCompleted && attempt < maxAttempts)
                {
                    attempt++;
                    _logger?.LogInformation("({Attempt}/{MaxAttempts}) Polling subscription usage job: {JobId}", attempt, maxAttempts, subUserJob.id);

                    try
                    {
                        HttpApiResponse jobResponse = JsonActions.JsonReturnHttpResponseGet(uri + subUserJob.selfUri, GCApiKey);

                        // Validate response using proper HTTP status code detection
                        if (string.IsNullOrWhiteSpace(jobResponse.Content))
                        {
                            _logger?.LogWarning("({Attempt}/{MaxAttempts}) Empty response received while polling subscription usage job.", attempt, maxAttempts);
                            continue;
                        }

                        // Handle different HTTP status codes appropriately
                        if (!jobResponse.IsSuccess)
                        {
                            if (jobResponse.StatusCode == 504 || jobResponse.Content.Contains("\"gateway.timeout\"") || jobResponse.Content.Contains("\"status\":504"))
                            {
                                _logger?.LogWarning("({Attempt}/{MaxAttempts}) Received gateway timeout from API.", attempt, maxAttempts);
                            }
                            else
                            {
                                _logger?.LogError("({Attempt}/{MaxAttempts}) API Error while polling subscription usage job: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                                    attempt, maxAttempts, jobResponse.StatusCode, jobResponse.StatusDescription, jobResponse.Content);
                            }
                            continue;
                        }

                        if (jobResponse.Content != "{}")
                        {
                            try
                            {
                                userJob = JsonConvert.DeserializeObject<SubUsers.UserUsageJob>(jobResponse.Content,
                                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                            }
                            catch (Exception jsonEx)
                            {
                                _logger?.LogError(jsonEx, "({Attempt}/{MaxAttempts}) JSON deserialization error while polling subscription usage job: {Message}. Response content: {Content}",
                                    attempt, maxAttempts, jsonEx.Message, jobResponse.Content);
                                continue;
                            }

                            if (userJob.status == "IN_PROGRESS")
                            {
                                _logger?.LogInformation("({Attempt}/{MaxAttempts}) Subscription usage job still in progress.", attempt, maxAttempts);
                            }
                            else if (userJob.status == "SUCCEEDED")
                            {
                                downloadURL = userJob.resultDownloadUrl;
                                jobCompleted = true;
                                _logger?.LogInformation("({Attempt}/{MaxAttempts}) Subscription usage job succeeded.", attempt, maxAttempts);
                            }
                            else
                            {
                                _logger?.LogWarning("({Attempt}/{MaxAttempts}) Subscription usage job returned status: {Status}.", attempt, maxAttempts, userJob.status);
                                jobCompleted = true;
                            }
                        }
                        else
                        {
                            _logger?.LogWarning("({Attempt}/{MaxAttempts}) Empty JSON response received.", attempt, maxAttempts);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "({Attempt}/{MaxAttempts}) Error polling subscription usage job: {Error}", attempt, maxAttempts, ex.ToString());
                    }

                    if (!jobCompleted)
                    {
                        if (firstRetryTime == null)
                        {
                            firstRetryTime = DateTime.UtcNow;
                        }
                        TimeSpan elapsed = DateTime.UtcNow - firstRetryTime.Value;
                        int delay;
                        if (elapsed < TimeSpan.FromMinutes(5))
                        {
                            delay = (attempt == 1) ? 1000 : 3000;
                        }
                        else if (elapsed < TimeSpan.FromMinutes(10))
                        {
                            delay = 9000;
                        }
                        else
                        {
                            delay = 27000;
                        }
                        _logger?.LogInformation("({Attempt}/{MaxAttempts}) Subscription usage job not complete; elapsed: {Elapsed}. Waiting {Delay} ms before next attempt.", attempt, maxAttempts, elapsed, delay);
                        Thread.Sleep(delay);
                    }
                }

                if (!jobCompleted)
                {
                    _logger?.LogError("Exceeded maximum polling attempts without subscription usage job completion.");
                    return subscriptionData;
                }

                int totalCsvRows = 0;
                int totalRecordsAdded = 0;
                if (!string.IsNullOrEmpty(downloadURL))
                {
                    _logger?.LogDebug("Downloading CSV from: {DownloadURL}", downloadURL);
                    Thread.Sleep(500);
                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(downloadURL);
                    request.Method = WebRequestMethods.Http.Get;
                    request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;

                    using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                    {
                        Stream responseStream = response.GetResponseStream()!;
                        StreamReader readStream = response.CharacterSet == null
                            ? new StreamReader(responseStream)
                            : new StreamReader(responseStream, System.Text.Encoding.GetEncoding(response.CharacterSet));
                        string csvString = readStream.ReadToEnd();
                        response.Close();
                        readStream.Close();

                        string[] csvEntries = csvString.Split(new string[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                        totalCsvRows = csvEntries.Length;
                        _logger?.LogInformation("CSV download complete. Total CSV rows: {TotalCsvRows}", totalCsvRows);
                        string[] headers = csvEntries[0].Split(',');
                        if (csvEntries.Length > 2 && headers.Length > 2)
                        {
                            for (int i = 1; i < csvEntries.Length; i++)
                            {
                                string csvRow = csvEntries[i];
                                string[] csvRowData = csvRow.Split(',');
                                for (int j = 2; j < csvRowData.Length - 1; j++)
                                {
                                    if (!csvRowData[0].Equals("Total", StringComparison.OrdinalIgnoreCase))
                                    {
                                        try
                                        {
                                            DataRow subUserRow = subscriptionData.NewRow();
                                            string datePart = csvRowData[0].Substring(0, 10);
                                            subUserRow["date"] = datePart;
                                            subUserRow["userlogin"] = csvRowData[1];
                                            subUserRow["licensename"] = headers[j];
                                            subUserRow["secs"] = ConvertSeconds(csvRowData[j]);
                                            subUserRow["hoursstr"] = csvRowData[j];
                                            subUserRow["keyid"] = UCAUtils.GetStableHashCode($"{datePart}|{csvRowData[1]}|{headers[j]}");
                                            subscriptionData.Rows.Add(subUserRow);
                                            totalRecordsAdded++;
                                        }
                                        catch (ConstraintException)
                                        {
                                            _logger?.LogDebug("Duplicate subscription usage record skipped.");
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogWarning(ex, "Error processing subscription usage CSV row: {Error}", ex.ToString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                _logger?.LogInformation("Subscription usage job completed in ({Attempt}/{MaxAttempts}) attempts. CSV Rows: {CsvRows}. Records Added: {RecordsAdded}.",
                                          attempt, maxAttempts, totalCsvRows, totalRecordsAdded);
            }
            else
            {
                _logger?.LogWarning("No valid response received when requesting subscription usage CSV.");
            }

            _logger?.LogInformation("GetSubUserUsage completed. Total subscription usage records: {Count}", subscriptionData.Rows.Count);
            return subscriptionData;
        }

        #endregion

        #region Subscription Overview Data

        public DataTable GetSubscriptionOverViewDatafromGC()
        {
            _logger?.LogInformation("Starting GetSubscriptionOverViewDatafromGC");
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();

            DataTable subscriptionData = dbUtil.CreateInMemTable("suboverviewData");
            string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            string periodBilling = JsonActions.JsonReturnString(uri + "/platform/api/v2/billing/periods?periodGranularity=month", GCApiKey);
            if (!string.IsNullOrWhiteSpace(periodBilling) && periodBilling != "{}")
            {
                dynamic billingPeriods = JsonConvert.DeserializeObject<dynamic>(periodBilling,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                // Check for Genesys Cloud API error responses using centralized error handling
                if (GenesysErrorHandler.IsErrorResponse(periodBilling))
                {
                    var errorResult = GenesysErrorHandler.CreateErrorResult(periodBilling);

                    // ONLY handle gracefully if it's specifically the "No billing status found" error
                    if (errorResult.ErrorCode.Contains("404") && errorResult.ErrorMessage.Contains("No billing status found"))
                    {
                        _logger?.LogWarning("No billing status found for this organization. This may be expected for organizations without billing data. Error: {ErrorMessage}", errorResult.ErrorMessage);
                        _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data due to missing billing status. Total records: 0");
                        return subscriptionData; // Return empty DataTable - graceful exit
                    }
                    else
                    {
                        // For all other API errors, throw exception to maintain existing error behavior
                        GenesysErrorHandler.LogError(_logger, "Billing Periods API", "Retrieving billing periods", periodBilling, LogLevel.Error);
                        throw new InvalidOperationException($"API error when retrieving billing periods: {errorResult.ErrorMessage}");
                    }
                }

                // Fallback: Check for Genesys Cloud API error responses (actual API error format) for dynamic objects
                // Example: {"message":"No billing status found for org 62a87c35-e61a-4297-b6ae-2002ee71af4c","code":"resourcenotfounderror","status":404,...}
                if (billingPeriods.message != null && billingPeriods.status != null)
                {
                    string errorMessage = billingPeriods.message?.ToString() ?? "Unknown error";
                    string errorCode = billingPeriods.code?.ToString() ?? "Unknown";
                    int statusCode = billingPeriods.status ?? 0;

                    // ONLY handle gracefully if it's specifically the "No billing status found" error
                    if (statusCode == 404 && errorMessage.Contains("No billing status found"))
                    {
                        _logger?.LogWarning("No billing status found for this organization (HTTP 404). This may be expected for organizations without billing data. Error: {ErrorMessage}", errorMessage);
                        _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data due to missing billing status. Total records: 0");
                        return subscriptionData; // Return empty DataTable - graceful exit
                    }
                    else
                    {
                        // For all other API errors, throw exception to maintain existing error behavior
                        _logger?.LogError("API error when retrieving billing periods: HTTP {StatusCode} - {ErrorCode} - {ErrorMessage}", statusCode, errorCode, errorMessage);
                        throw new InvalidOperationException($"API error when retrieving billing periods: HTTP {statusCode} - {errorCode} - {errorMessage}");
                    }
                }

                // Check for JsonReturnString error format (fallback for other error scenarios) - already handled above by GenesysErrorHandler
                // This section is kept for backward compatibility with dynamic object parsing
                // Example: {"error": true, "message": "Resource not found", "statusCode": "NotFound"}
                if (billingPeriods.error != null && billingPeriods.error == true)
                {
                    string errorMessage = billingPeriods.message?.ToString() ?? "Unknown error";
                    string statusCode = billingPeriods.statusCode?.ToString() ?? "Unknown";

                    // ONLY handle gracefully if it's a NotFound that could be related to billing status
                    if (statusCode == "NotFound")
                    {
                        _logger?.LogWarning("Resource not found when retrieving billing periods. This may be expected for organizations without billing data. Error: {ErrorMessage}", errorMessage);
                        _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data due to missing billing status. Total records: 0");
                        return subscriptionData; // Return empty DataTable - graceful exit
                    }
                    else
                    {
                        // For all other error types, throw exception to maintain existing error behavior
                        _logger?.LogError("API error when retrieving billing periods: {StatusCode} - {ErrorMessage}", statusCode, errorMessage);
                        throw new InvalidOperationException($"API error when retrieving billing periods: {statusCode} - {errorMessage}");
                    }
                }

                // Check if entities property exists and is not null
                if (billingPeriods.entities == null)
                {
                    _logger?.LogWarning("Billing periods response does not contain entities data.");
                    _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed with no data. Total records: 0");
                    return subscriptionData; // Return empty DataTable
                }

                DateTime maxSyncDate = dbUtil.GetSyncLastUpdate("suboverviewdata");
                IEnumerable<dynamic> periodsEnumerable = ((IEnumerable<dynamic>)billingPeriods.entities)
                                                            .OrderBy(p => (DateTime)p.startDate);

                if (maxSyncDate != null && maxSyncDate != DateTime.MinValue)
                {
                    int lastPeriodIndex = -1;
                    int index = 0;
                    foreach (dynamic period in periodsEnumerable)
                    {
                        DateTime startDate = (DateTime)period.startDate;
                        DateTime endDate = (DateTime)period.endDate;
                        if (maxSyncDate >= startDate && maxSyncDate <= endDate)
                        {
                            lastPeriodIndex = index;
                            break;
                        }
                        index++;
                    }
                    if (lastPeriodIndex != -1)
                    {
                        periodsEnumerable = periodsEnumerable.Skip(Math.Max(0, lastPeriodIndex - 1));
                    }
                    else
                    {
                        periodsEnumerable = periodsEnumerable.Reverse().Take(1).Reverse();
                    }
                }

                var periodsArray = periodsEnumerable.ToArray();
                foreach (var period in periodsArray)
                {
                    string id = period.id.ToString();
                    string startDateStr = period.startDate.ToString();
                    string endDateStr = period.endDate.ToString();

                    _logger?.LogInformation("Processing billing period with ID: {Id}, StartDate: {StartDate}, EndDate: {EndDate}", id, startDateStr, endDateStr);
                    string jsonString = JsonActions.JsonReturnString(uri + "/api/v2/billing/subscriptionoverview?periodEndingTimestamp=" + id, GCApiKey);
                    SubsOver.SubOverview subsOverview = new SubsOver.SubOverview();
                    if (!string.IsNullOrWhiteSpace(jsonString) && jsonString != "{}")
                    {
                        subsOverview = JsonConvert.DeserializeObject<SubsOver.SubOverview>(jsonString,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        DateTime rowDate = DateTime.UtcNow.Date;

                        if (subsOverview.usages != null && subsOverview.usages.Count() > 0)
                        {
                            foreach (SubsOver.Usage license in subsOverview.usages)
                            {
                                try
                                {
                                    DataRow licenseRow = subscriptionData.NewRow();
                                    licenseRow["keyid"] = $"{startDateStr}|{(string)license.partNumber}|{(string)license.name}|{(license.grouping != null ? license.grouping.ToString() : "")}";
                                    licenseRow["rowdate"] = rowDate;
                                    licenseRow["licname"] = license.name;
                                    licenseRow["partnumber"] = license.partNumber;
                                    licenseRow["grouping"] = license.grouping;
                                    licenseRow["unitofmeasuretype"] = license.unitOfMeasureType;
                                    licenseRow["usagequantity"] = license.usageQuantity;
                                    licenseRow["prepayQuantity"] = license.prepayQuantity ?? "0";
                                    licenseRow["overageprice"] = license.overagePrice != null ? Convert.ToDouble(license.overagePrice) : 0.0;
                                    licenseRow["iscancellable"] = license.isCancellable;
                                    licenseRow["isthirdparty"] = license.isThirdParty;
                                    licenseRow["startdate"] = startDateStr;
                                    licenseRow["enddate"] = endDateStr;
                                    subscriptionData.Rows.Add(licenseRow);
                                }
                                catch (ConstraintException)
                                {
                                    _logger?.LogDebug("Duplicate subscription overview record skipped for billing period {Id}.", id);
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogWarning(ex, "Error processing subscription overview record for billing period {Id}: {Error}", id, ex.ToString());
                                }
                            }
                            _logger?.LogInformation("Successfully processed billing period {Id} with {Count} usage records.", id, subsOverview.usages.Count());
                        }
                        else
                        {
                            _logger?.LogInformation("Billing period {Id} (Start: {StartDate}, End: {EndDate}) returned no usage records.", id, startDateStr, endDateStr);
                        }
                    }
                    else
                    {
                        _logger?.LogInformation("Billing period {Id} (Start: {StartDate}, End: {EndDate}) returned no data.", id, startDateStr, endDateStr);
                    }
                }
            }
            else
            {
                _logger?.LogWarning("No billing periods data returned from the API.");
            }
            _logger?.LogInformation("GetSubscriptionOverViewDatafromGC completed. Total records: {Count}", subscriptionData.Rows.Count);
            return subscriptionData;
        }

        #endregion

        #region Team Data

        public DataTable GetTeamMembersfromGC(DataTable teamDetails)
        {
            _logger?.LogInformation("Starting GetTeamMembersfromGC");
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();
            DataTable teamMembers = dbUtil.CreateInMemTable("teammemberdata");

            foreach (DataRow team in teamDetails.Rows)
            {
                bool nextPage = true;
                string nextPageStr = "";
                while (nextPage)
                {
                    try
                    {
                        string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                        string urlString = string.IsNullOrWhiteSpace(nextPageStr)
                            ? $"{uri}/api/v2/teams/{team["id"]}/members?expand=entities&pageSize=100"
                            : $"{uri}{nextPageStr}";
                        _logger?.LogDebug("Requesting team members for team ID: {TeamId} using URL: {UrlString}", team["id"], urlString);
                        string jsonString = JsonActions.JsonReturnString(urlString, GCApiKey);
                        TeamsMembers.Rootobject teamData = JsonConvert.DeserializeObject<TeamsMembers.Rootobject>(jsonString,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }) ?? new TeamsMembers.Rootobject();

                        if (!string.IsNullOrWhiteSpace(jsonString) && jsonString != "{}")
                        {
                            foreach (TeamsMembers.Entity user in teamData.entities)
                            {
                                DataRow teamRow = teamMembers.NewRow();
                                teamRow["keyid"] = $"{user.id}|{team["id"]}";
                                teamRow["teamid"] = team["id"];
                                teamRow["userid"] = user.id;
                                teamRow["updated"] = DateTime.UtcNow;
                                teamMembers.Rows.Add(teamRow);
                            }
                            if (!string.IsNullOrWhiteSpace(teamData.nextUri))
                            {
                                nextPageStr = teamData.nextUri;
                                _logger?.LogDebug("More team members available. Next page URI: {NextUri}", nextPageStr);
                            }
                            else
                            {
                                nextPage = false;
                            }
                        }
                        else
                        {
                            nextPage = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning("Error retrieving team members for team ID {TeamId}: {Error}", team["id"], ex.ToString());
                        nextPage = false;
                    }
                }
            }
            _logger?.LogInformation("GetTeamMembersfromGC completed. Total team member records: {Count}", teamMembers.Rows.Count);
            return teamMembers;
        }

        public DataTable GetTeamDetailsfromGC()
        {
            _logger?.LogInformation("Starting GetTeamDetailsfromGC");
            DBUtils.DBUtils dbUtil = new DBUtils.DBUtils();
            dbUtil.Initialize();
            DataTable teamDetails = dbUtil.CreateInMemTable("teamdetails");
            try
            {
                string uri = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                string jsonString = JsonActions.JsonReturnString(uri + "/api/v2/teams", GCApiKey);
                Teams.Teams teamData = JsonConvert.DeserializeObject<Teams.Teams>(jsonString,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }) ?? new Teams.Teams();

                if (!string.IsNullOrWhiteSpace(jsonString) && jsonString != "{}")
                {
                    foreach (Teams.Entity indTeam in teamData.entities)
                    {
                        DataRow teamRow = teamDetails.NewRow();
                        teamRow["id"] = indTeam.id;
                        teamRow["name"] = indTeam.name;
                        teamRow["division"] = indTeam.division;
                        teamRow["datecreated"] = indTeam.dateCreated;
                        teamRow["datemodified"] = indTeam.dateModified;
                        teamRow["membercount"] = indTeam.memberCount;
                        teamRow["updated"] = DateTime.UtcNow;
                        teamDetails.Rows.Add(teamRow);
                    }
                    _logger?.LogInformation("GetTeamDetailsfromGC completed. Total teams: {Count}", teamDetails.Rows.Count);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning("Error retrieving team details: {Error}", ex.ToString());
            }
            return teamDetails;
        }

        #endregion

        #region Helper Methods

        private decimal ConvertSeconds(string timeIn)
        {
            if (TimeSpan.TryParse(timeIn, out TimeSpan ts))
            {
                return (decimal)ts.TotalSeconds;
            }
            else
            {
                _logger?.LogWarning("Failed to parse time string: {TimeIn}", timeIn);
                return 0;
            }
        }

        #endregion
    }
}
